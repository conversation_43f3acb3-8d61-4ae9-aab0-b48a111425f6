<?xml version="1.0"?>
<odoo>
    <data>
        <record id="inherit_view_comparison_form" model="ir.ui.view">
            <field name="name">inherit.view.comparison.form</field>
            <field name="model">comparison</field>
            <field name="inherit_id" ref="meta_purchase_comparison_system.view_comparison_form" />
            <field name="arch" type="xml">
                <xpath expr="//header/button[@name='confirm_cs']" position="attributes">
                    <attribute name="groups">meta_approval_matrix_cs.procure_user_cs_confirmation</attribute>
                </xpath>
                <xpath expr="//header/button[@name='confirm_cs']" position="before">
                    <button name="send_to_technical" type="object" string="Send for Approval" class="oe_highlight" invisible="state not in ('draft')"/>
                    <!-- <button name="send_to_technical" type="object" string="Send to Procurement Approval" class="oe_highlight" invisible="state not in ('draft') or type_pr_tender != 'multiple_ho_pr'"/> -->
                    
                    <button name="techincal_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('technical')"/>
                    <button name="department_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('department')"/>
                    <button name="domain_exp_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('domain_expert')"/>
                    <button name="prcuremnet_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('procurement')"/>
                    <button name="scm_hod_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('scm_hod')"/>
                    <button name="cfo_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('cfo')"/>

                    <button name="project_manager_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('project_manager')"/>
                    <button name="field_finance_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('field_finance')"/>
                    <button name="rc_approve" type="object" string="Approve"
                        class="oe_highlight" invisible="state not in ('regional_coordinator')"/>
                </xpath>
                <field name="company_id" position="before">
                    <field name="initiator_member_employess" readonly="1"/>

                    <field name="tech_member_domain" invisible="1"/>
                    <field name="tech_member_employess" readonly="state != 'technical'" invisible="type_pr_tender not in ('single_ho_pr', 'single_field_pr')"
                        required="tech_member_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>

                    <field name="dept_member_domain" invisible="1"/>
                    <field name="dept_member_employess" readonly="state != 'department'" invisible="type_pr_tender not in ('single_ho_pr')"
                        required="dept_member_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>

                    <field name="procure_member_domain" invisible="1"/>
                    <field name="procure_member_employess" readonly="state != 'procurement'" invisible="type_pr_tender not in ('single_ho_pr', 'multiple_ho_pr')"
                        required="procure_member_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>

                    <field name="scm_hod_member_domain" invisible="1"/>
                    <field name="scm_hod_member_employess" readonly="state != 'scm_hod'" invisible="type_pr_tender not in ('single_ho_pr', 'multiple_ho_pr')"
                        required="scm_hod_member_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>

                    <field name="project_manager_domain" invisible="1"/>
                    <field name="project_manager_emploee" readonly="state != 'project_manager'" invisible="type_pr_tender != 'single_field_pr'"
                        required="project_manager_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>

                    <field name="field_finance_domain" invisible="1"/>
                    <field name="field_financer_emploee" readonly="state != 'field_finance'" invisible="type_pr_tender != 'single_field_pr'"
                        required="field_finance_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>

                    <field name="rc_domain" invisible="1"/>
                    <field name="rc_emploee" readonly="state != 'regional_coordinator'" invisible="type_pr_tender != 'single_field_pr'"
                        required="rc_domain" 
                        options="{'no_create': True,'no_create_edit': True}"/>
                </field>
            </field>
        </record>

        <record id="inherit_view_tender_view_for_initiator" model="ir.ui.view">
            <field name="name">inherit.view.tender.view.for.initiator</field>
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form" />
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('oe_button_box')]//field[@name='order_count']" position="attributes">
                    <attribute name="string">Quotations</attribute>
                </xpath>
                <field name="vendor_id" position="after">
                    <field name="initiator_member_domain" invisible="1"/>
                    <field name="initiator_member_employess" options="{'no_create': True,'no_create_edit': True}"/>
                    <field name="pr_requester" readonly="1"/>
                    <field name="pr_approver"  readonly="1"/>
                </field>
            </field>
        </record>

        <record model="ir.actions.act_window" id="purchase_requisition.action_purchase_requisition_list">
            <field name="name">Request for Quotations</field>
            <field name="res_model">purchase.order</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('requisition_id','=',active_id), ('comparison_id','=',False)]</field>
            <field name="context">{
                "default_requisition_id":active_id,
                }
            </field>
        </record>

    </data>
</odoo>