# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auth_session_timeout
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-31 11:58+0000\n"
"PO-Revision-Date: 2016-08-31 11:58+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2016\n"
"Language-Team: Croatian (https://www.transifex.com/oca/teams/23907/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: auth_session_timeout
#: model:ir.model,name:auth_session_timeout.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: auth_session_timeout
#: model:ir.model,name:auth_session_timeout.model_ir_config_parameter
msgid "System Parameter"
msgstr ""

#. module: auth_session_timeout
#: model:ir.model,name:auth_session_timeout.model_res_users
msgid "User"
msgstr ""

#~ msgid "Users"
#~ msgstr "Korisnici"
