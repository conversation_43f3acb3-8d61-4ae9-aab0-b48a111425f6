<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Server action for the Supervisor Change print option -->
    <record id="action_print_supervisor_change" model="ir.actions.server">
        <field name="name">Print Supervisor Change Letter</field>
        <field name="model_id" ref="hr.model_hr_employee"/>
        <field name="binding_model_id" ref="hr.model_hr_employee"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">
# No indentation here - make sure this line is flush with the left margin
employee_id = env.context.get('active_id')
if employee_id:
    action = {
        'name': 'Supervisor Change Letter',
        'type': 'ir.actions.act_window',
        'res_model': 'supervisor.change.wizard',
        'view_mode': 'form',
        'target': 'new',
        'context': {
            'default_employee_id': employee_id,
            'default_supervisor_change_date': env.context.get('date', False),
        }
    }
    result = action
        </field>
    </record>
</odoo>