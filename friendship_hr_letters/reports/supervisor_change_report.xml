<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_report_supervisor_change" model="ir.actions.report">
        <field name="name">Change of Reporting Supervisor</field>
        <field name="model">hr.employee</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">friendship_hr_letters.report_supervisor_change</field>
        <field name="report_file">friendship_hr_letters.report_supervisor_change</field>
        <field name="print_report_name">'Change of Reporting Supervisor - %s' % (object.name)</field>
        <field name="binding_model_id" ref="hr.model_hr_employee"/>
        <field name="binding_type">report</field>
    </record>

    <template id="report_supervisor_change">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="row">
                            <div class="col-6">
                                Ref: friendship/HR/2018/059
                            </div>
                            <div class="col-6 text-right">
                                Personnel/Confidential
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                Date: <t t-esc="report_data.get('supervisor_change_date')"/>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <strong t-field="o.name"/>
                                <br/>
                                <span t-field="o.job_id.name"/>
                                <br/>
                                <t t-if="o.barcode">
                                    Employee ID: <span t-field="o.barcode"/>
                                    <br/>
                                </t>
                                <br/>
                                <span t-field="o.work_location_id.name"/>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-12">
                                <strong>Subject: Change of Reporting Supervisor</strong>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                Dear <strong t-field="o.name"/>,
                                <br/>
                                Please be informed that your reporting supervisor will be changed from the <t t-if="report_data.get('supervisor_change_last_job_name')"><span t-esc="report_data.get('supervisor_change_last_job_name')"/></t> <t t-else="">Executive Director</t> to
                                <t t-if="report_data.get('supervisor_change_new_job_id')"><span t-esc="report_data.get('supervisor_change_new_job_name')"/></t><t t-else="">Manager - Fundraising &amp; Partnership Development</t> with immediate effect.
                                <br/>
                                Other terms and conditions of your services will remain unchanged.
                            </div>
                        </div>
                        <div class="row mt-5">
                            <div class="col-12">
                                Yours Sincerely,
                                <br/>
                                <br/>
                                __________________________________
                                <br/>
                                <t t-if="report_data.get('supervisor_change_employer_name')"><strong t-esc="report_data.get('supervisor_change_employer_name')"/></t>
                                <br/>
                                <span t-esc="report_data.get('supervisor_change_employer_designation')"/>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-12">
                                CC:
                                <t t-foreach="report_data.get('supervisor_change_cc_ids', [])" t-as="cc">
                                    <t t-if="cc[1]"><span t-esc="cc[1]"/></t>
                                    <t t-if="cc[2]"> (<span t-esc="cc[2]"/>)</t><br/>
                                </t>
                                Personal File
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
