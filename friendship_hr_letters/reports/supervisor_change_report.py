# -*- coding: utf-8 -*-
from odoo import api, fields, models

class SupervisorChangeReport(models.AbstractModel):
    _name = 'report.friendship_hr_letters.report_supervisor_change'
    _description = 'Supervisor Change Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        
        if not data:
            data = {}
            
        if docids:
            employees = self.env['hr.employee'].browse(docids)
        elif data.get('employee_id'):
            employees = self.env['hr.employee'].browse(data.get('employee_id'))
        else:
            employees = self.env['hr.employee']
        
        return {
            'doc_ids': docids,
            'doc_model': 'hr.employee',
            'docs': employees,
            'report_data': data,
        }