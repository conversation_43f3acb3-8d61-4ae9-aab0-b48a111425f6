# -*- coding: utf-8 -*-
from odoo import api, fields, models

class SupervisorChangeWizard(models.TransientModel):
    _name = 'supervisor.change.wizard'
    _description = 'Wizard to Change Reporting Supervisor Document'

    employee_id = fields.Many2one('hr.employee', string='Employee', required=True)
    supervisor_change_date = fields.Date(string='Date', default=fields.Date.today(), required=True)
    
    supervisor_change_last_job_id = fields.Many2one(
        'hr.job', string='Last Manager', required=True
    )
    supervisor_change_new_job_id = fields.Many2one(
        'hr.job', string='New Manager', required=True
    )
    
    supervisor_change_employer_id = fields.Many2one('res.partner', string='Employer', required=True)
    supervisor_change_employer_designation = fields.Char(string='Employer Designation', required=True)
    supervisor_change_cc_ids = fields.Many2many('friendship.letter.cc.template', string='CC')

    @api.onchange('supervisor_change_employer_id')
    def _onchange_employer_id(self):
        """Auto-fill designation from employer's function or set default"""
        if self.supervisor_change_employer_id:
            # If employer has a function defined, use it
            if self.supervisor_change_employer_id.function:
                self.supervisor_change_employer_designation = self.supervisor_change_employer_id.function
    
    def print_report(self):
        self.ensure_one()
        import logging
        _logger = logging.getLogger(__name__)
        
        report_data = {
            'employee_id': self.employee_id.id,
            'supervisor_change_date': self.supervisor_change_date.strftime('%d/%m/%Y') if self.supervisor_change_date else '',
            'supervisor_change_employer_id': self.supervisor_change_employer_id.id if self.supervisor_change_employer_id else False,
            'supervisor_change_employer_name': self.supervisor_change_employer_id.name if self.supervisor_change_employer_id else '',
            'supervisor_change_employer_designation': self.supervisor_change_employer_designation,
            'supervisor_change_cc_ids': [(cc.id, cc.name, cc.description) for cc in self.supervisor_change_cc_ids],
            'supervisor_change_last_job_id': self.supervisor_change_last_job_id.id if self.supervisor_change_last_job_id else False,
            'supervisor_change_last_job_name': self.supervisor_change_last_job_id.name if self.supervisor_change_last_job_id else '',
            'supervisor_change_new_job_id': self.supervisor_change_new_job_id.id if self.supervisor_change_new_job_id else False,
            'supervisor_change_new_job_name': self.supervisor_change_new_job_id.name if self.supervisor_change_new_job_id else '',
        }
        
        _logger.info("Report Data from wizard: %s", report_data)
        
        return self.env.ref('friendship_hr_letters.action_report_supervisor_change').report_action(self.employee_id, data=report_data)