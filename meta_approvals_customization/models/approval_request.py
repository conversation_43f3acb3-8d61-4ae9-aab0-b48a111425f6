from odoo import models, fields, api


class ApprovalRequest(models.Model):
    _inherit = "approval.request"



    total_amount = fields.Monetary(string="Total Amount", compute="_compute_total_amount")
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, default=lambda self: self.env.company.currency_id)


    #__________________budget line ____________

    budget_line_ids = fields.One2many('approval.budget.line','approval_id',string="Budget Lines") 
    total = fields.Monetary(string="Total Amount", compute="_compute_total_req_amount")
    date = fields.Datetime(string="Date", default=fields.Datetime.now)
    in_bank = fields.Float(string="Closing Balance (In Bank)")
    in_hand = fields.Float(string="Closing Balance (In Hand)")
    total_closing_balance = fields.Float(string = "Total Closing Balance", compute="_compute_total_closing_balance")
    mfr_location_id = fields.Many2one("mfr.location", string="Location")


 


    @api.depends("product_line_ids.price_subtotal")
    def _compute_total_amount(self):
        for reqest in self:
            reqest.total_amount = sum(reqest.product_line_ids.mapped("price_subtotal"))


    @api.depends("in_hand","in_bank")
    def _compute_total_closing_balance(self):
        for request in self:
            request.total_closing_balance = request.in_bank + request.in_hand


    @api.depends("budget_line_ids.req_amount")
    def _compute_total_req_amount(self):
        for reqest in self:
            reqest.total = sum(reqest.budget_line_ids.mapped("req_amount")) - reqest.total_closing_balance


