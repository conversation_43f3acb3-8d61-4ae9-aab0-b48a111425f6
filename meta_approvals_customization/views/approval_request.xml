<?xml version="1.0" encoding="UTF-8" ?>

<odoo>
    <record id="view_approval_request_form_inherit_total" model="ir.ui.view">
        <field name="name">approval.request.form.inherit.total</field>
        <field name="model">approval.request</field>
        <field name="inherit_id" ref="approvals.approval_request_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='request_details']" position="inside">
                <field name="total_amount" invisible="1"/>
                <field name="company_id"/>
                <field name="in_bank"/>
                <field name="in_hand"/>
                <field name="total_closing_balance"/>

                <field name="currency_id" invisible="1"/>

            </xpath>
            <xpath expr="//field[@name='amount']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='date_confirmed']" position="after">
                <field name="mfr_location_id"/>
            </xpath>



            <xpath expr="//page[@name='products']" position="after">
                <page nmae="budget_line" string="Budget Line">
                    <field name="budget_line_ids">
                        <tree editable="bottom">
                            <field name="budget_id"/>
                            <field name="available_budget"/>
                            <field name="budget_type"/>
                            <field name="req_amount"/>
                            <field name="remarks"/>
                            <field name="company_domain" column_invisible="1"/>
                        </tree>
                    </field>
                    <group class="oe_subtotal_footer oe_right" colspan="2" name="approval_total">
                        <field name="total" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        <field name="currency_id" invisible="1"/>
                    </group>
                </page>
            </xpath>
            <xpath expr="//page[@name='products']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//page[@name='approvers']/field[@name='approver_ids']/tree/field[@name='user_id']" position="after">
                <field name="employee_id"/>
            </xpath>


        </field>
    </record>
</odoo>