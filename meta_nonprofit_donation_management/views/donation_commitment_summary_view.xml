<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- Tree View -->
    <record id="view_donation_commitment_summary_tree" model="ir.ui.view">
        <field name="name">donation.commitment.summary.tree</field>
        <field name="model">donation.commitment.summary</field>
        <field name="arch" type="xml">
            <tree string="Donation Commitments Tracking">
                <field name="name"/>
                <field name="project_id"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="program_id"/>
                <field name="expansion_budget"/>
                <field name="cur_year_budget"/>
                <field name="last_year_budget"/>
                <field name="total_commitment"/>
                <field name="deficiit"/>
                <field name="deficit_percentage"/>

            </tree>
        </field>
    </record>


    <!-- Form View -->
    <record id="view_donation_commitment_summary_form" model="ir.ui.view">
        <field name="name">donation.commitment.summary.form</field>
        <field name="model">donation.commitment.summary</field>
        <field name="arch" type="xml">
            <form string="Donation Commitment">
                <header>
                    <button name="action_confirm" type="object" invisible="state != 'draft'" string="Confirm" class="oe_highlight" id="bid_confirm" data-hotkey="q"/>
                    <button name="action_cancel" type="object" invisible="state != 'draft'" string="Cancel" id="bid_cancel" data-hotkey="c"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed" readonly="1"/>
                </header>
                <sheet>

                    <div class="oe_title">
                        <h1 class="d-flex">
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="project_id"/>
                            <field name="program_id"/>
                            <field name="expansion_budget"/>
                            <field name="cur_year_budget"/>
                            <field name="last_year_budget"/>

                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                            <field name="total_commitment"/>
                            <field name="deficiit"/>
                            <field name="deficit_percentage"/>

                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_donation_commitment_summary" model="ir.actions.act_window">
        <field name="name">Donation Commitment Tracking</field>
        <field name="res_model">donation.commitment.summary</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first donation commitment tracking
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_donation_commitment_root" 
        name="Donations" 
        web_icon="meta_nonprofit_donation_management,static/description/icon.png"
        sequence="10"/>

    <menuitem id="menu_donation_commitment_summary" name="Donation Commitment Tracking" parent="menu_donation_commitment_root" action="action_donation_commitment_summary" sequence="10"/>

</odoo>
