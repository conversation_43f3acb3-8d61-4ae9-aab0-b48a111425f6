<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- Tree View -->
    <record id="view_donation_commitment_tree" model="ir.ui.view">
        <field name="name">donation.commitment.tree</field>
        <field name="model">donation.commitment</field>
        <field name="arch" type="xml">
            <tree string="Donation Commitments">
                <field name="project_id"/>
                <field name="program_id"/>
                <field name="remarks"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="total_budget"/>
                <field name="office_id"/>
                <field name="donor_id"/>
                <field name="opening_balance"/>
                <field name="commitment_amount"/>
                <field name="received_amount"/>
                <field name="remaining_amount"/>
                <field name="remaining_bdt"/>
                <field name="state" optional="hide"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_donation_commitment_form" model="ir.ui.view">
        <field name="name">donation.commitment.form</field>
        <field name="model">donation.commitment</field>
        <field name="arch" type="xml">
            <form string="Donation Commitment">
                <header>
                    <button name="button_confirm" type="object" invisible="state != 'draft'" string="Confirm" class="oe_highlight" id="bid_confirm" data-hotkey="q"/>
                    <button name="action_cancel" type="object" invisible="state != 'draft'" string="Cancel" id="bid_cancel" data-hotkey="c"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed" readonly="1"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object" name="action_view_invoice" class="oe_stat_button" icon="fa-pencil-square-o" invisible="invoice_count == 0 or state in ('draft')">
                            <field name="invoice_count" widget="statinfo" string="Invoices"/>
                            <field name="invoice_ids" invisible="1"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1 class="d-flex">
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <!-- <field name="year"/> -->
                            <field name="donation_year"/>
                            <field name="project_id"/>
                            <field name="program_id"/>
                            <field name="start_date"/>
                            <field name="end_date"/>
                            <field name="total_budget"/>
                            <field name="expected_date"/>
                            <field name="opening_balance"/>
                            <field name="remarks"/>
                        </group>
                        <group>
                            <field name="office_id"/>
                            <field name="currency_id"/>
                            <field name="donor_id"/>
                            <field name="commitment_amount"/>
                            <field name="received_amount"/>
                            <field name="invoice_ids" widget="many2many_tags"/>
                            <field name="remaining_amount"/>
                            <field name="remaining_bdt"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_donation_commitment" model="ir.actions.act_window">
        <field name="name">Donation Commitments</field>
        <field name="res_model">donation.commitment</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first donation commitment
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_donation_commitment_root" name="Donations" sequence="10"/>

    <menuitem id="menu_donation_commitment" name="Donation Commitments"
            parent="menu_donation_commitment_root" action="action_donation_commitment" sequence="10"/>

</odoo>
