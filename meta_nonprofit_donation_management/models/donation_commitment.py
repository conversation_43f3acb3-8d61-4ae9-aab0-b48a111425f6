from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime


class DonationCommitment(models.Model):
    _name = 'donation.commitment'
    _description = 'Donation Commitment'
    _order = 'id desc'
    
    
    name = fields.Char(string='Name', required=True, copy=False, readonly=False,default=lambda self: _('New'))
    # year = fields.Integer(string='Year')
    project_id = fields.Many2one('res.company', string='Project')
    program_id = fields.Many2one('project.program', string='Program')
    start_date = fields.Date(string='Start Date')
    end_date = fields.Date(string='End Date')
    expected_date = fields.Date(string='Expected Date')
    opening_balance = fields.Float(string='Opening Balance')
    remarks = fields.Text(string='Remarks')
    
    
    office_id = fields.Selection(
        string='Friendship Office',
        selection=[('valor1', 'valor1'), ('valor2', 'valor2')]
    )
    currency_id = fields.Many2one(
        'res.currency', string='Currency',
        default=lambda self: self.env.company.currency_id.id
    )
    donor_id = fields.Many2one('res.partner', string='Donor')
    commitment_amount = fields.Monetary(string='Commitment Amount', currency_field='currency_id')
    received_amount = fields.Monetary(string='Received Amount', currency_field='currency_id', compute="_compute_received_amount")
    invoice_count = fields.Integer(compute="_compute_invoice", string='Bill Count', copy=False, default=0, store=True)
    invoice_ids = fields.Many2many('account.move', string='Invoice')
    remaining_amount = fields.Monetary(string='Remaining Amount', store=True, currency_field='currency_id')
    remaining_bdt = fields.Float(string='Remaining BDT', store=True)
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft')
    
    total_budget = fields.Float(string='Total Budget', compute='_compute_total_budget', store=True)
    donation_year = fields.Selection(selection="_get_years", string="Year")

    def _get_years(self):
        year_list = []
        for y in range(datetime.now().year-20, datetime.now().year + 20):
            # year_str = str(y-1)+'-'+str(y)
            year_str = str(y)
            year_list.append((year_str, year_str))
        return year_list
    
    
    @api.depends('project_id')
    def _compute_total_budget(self):
        for rec in self:
            total = 0.00
            if rec.project_id:
                for budget in self.env['project.budget'].search([('company_id', '=', rec.project_id.id)]):
                    # Only consider budgets that are approved
                    total += budget.total_budget
            rec.total_budget = total
    
    
    
    
    def _compute_received_amount(self):
        for rec in self:
            # Calculate the received amount based on the PAID amount of invoices, not their total amount
            total_received = 0
            for invoice in rec.invoice_ids:
                # For each invoice, add the amount that has been paid
                total_received += invoice.amount_total - invoice.amount_residual
                
            rec.received_amount = total_received
            
            # Calculate the remaining amount
            rec.remaining_amount = rec.commitment_amount - total_received
            rec.remaining_bdt = rec.remaining_amount * rec.currency_id.rate
    
    
    
    @api.depends('invoice_ids')
    def _compute_invoice(self):
        for order in self:
            invoices = order.mapped('invoice_ids')
            order.invoice_count = len(invoices)
    
    
    
    def action_view_invoice(self, invoices=False):
        """This function returns an action that display existing invoices of
        given Donation Commitment. When only one found, show the invoices
        immediately.
        """
        if not invoices:
            self.invalidate_model(['invoice_ids'])
            invoices = self.invoice_ids

        result = self.env['ir.actions.act_window']._for_xml_id('account.action_move_out_invoice_type')
        # choose the view_mode accordingly
        if len(invoices) > 1:
            result['domain'] = [('id', 'in', invoices.ids)]
        elif len(invoices) == 1:
            res = self.env.ref('account.view_move_form', False)
            form_view = [(res and res.id or False, 'form')]
            if 'views' in result:
                result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
            else:
                result['views'] = form_view
            result['res_id'] = invoices.id
        else:
            result = {'type': 'ir.actions.act_window_close'}

        return result
    
    
    
    def action_cancel(self):
        for rec in self:
            rec.state = 'cancelled'
    
    def button_confirm(self):
        for rec in self:
            # Step 1: Get or create product named 'Donation'
            product = self.env['product.product'].search([('name', '=', 'Donation')], limit=1)
            if not product:
                raise ValidationError(_('Product "Donation" not found. Please create it first.'))

            # Step 2: Create the invoice
            invoice_vals = {
                'move_type': 'out_invoice',
                'partner_id': rec.donor_id.id,
                'currency_id': rec.currency_id.id,
                'invoice_date': fields.Date.context_today(self),
                'invoice_line_ids': [(0, 0, {
                    'product_id': product.id,
                    'quantity': 1,
                    'price_unit': rec.commitment_amount,
                    'name': 'Donation Commitment',
                    'account_id': product.property_account_income_id.id or product.categ_id.property_account_income_categ_id.id,
                })],
            }

            invoice = self.env['account.move'].create(invoice_vals)

            # Step 3: Link the invoice
            rec.invoice_ids = [(4, invoice.id)]

            # Step 4: Change state
            rec.state = 'confirmed'
