# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.tools.misc import unique
from odoo.osv import expression
import re


class ProductProduct(models.Model):
    _inherit = "product.template"

    product_extra_categ_id = fields.Many2one(
        "product.extra.field", string="Type of Item", ondelete="cascade"
    )
    product_attribute_id = fields.Many2one(
        "product.custom.attribute", string="Product Attribute", ondelete="cascade"
    )


# Added by Jony
class ProductProductInherit(models.Model):
    _inherit = "product.product"

    @api.depends(
        "name",
        "default_code",
        "product_tmpl_id",
        "product_extra_categ_id",
        "product_attribute_id",
    )
    @api.depends_context(
        "display_default_code",
        "seller_id",
        "company_id",
        "partner_id",
        "use_partner_name",
    )
    def _compute_display_name(self):
        def get_display_name(name, code, extra_categ, attribute):
            display_name_parts = []

            # Add extra category name if exists
            if extra_categ:
                display_name_parts.append(f"[{extra_categ}]")

            # Add product code if needed
            # if self._context.get('display_default_code', True) and code:
            #     display_name_parts.append(f'[{code}]')

            # Add main product name
            display_name_parts.append(name)

            # Add attribute name if exists
            if attribute:
                display_name_parts.append(attribute)

            return " ".join(display_name_parts)

        partner_id = (
            self._context.get("partner_id")
            if self.env.context.get("use_partner_name", True)
            else self.env["res.partner"]
        )
        if partner_id:
            partner_ids = [
                partner_id,
                self.env["res.partner"].browse(partner_id).commercial_partner_id.id,
            ]
        else:
            partner_ids = []
        company_id = self.env.context.get("company_id")
        # all user don't have access to seller and partner
        # check access and use superuser
        self.check_access_rights("read")
        self.check_access_rule("read")
        product_template_ids = self.sudo().product_tmpl_id.ids
        if partner_ids:
            # prefetch the fields used by the `display_name`
            supplier_info = (
                self.env["product.supplierinfo"]
                .sudo()
                .search_fetch(
                    [
                        ("product_tmpl_id", "in", product_template_ids),
                        ("partner_id", "in", partner_ids),
                    ],
                    [
                        "product_tmpl_id",
                        "product_id",
                        "company_id",
                        "product_name",
                        "product_code",
                    ],
                )
            )
            supplier_info_by_template = {}
            for r in supplier_info:
                supplier_info_by_template.setdefault(r.product_tmpl_id, []).append(r)
        for product in self.sudo():
            variant = (
                product.product_template_attribute_value_ids._get_combination_name()
            )
            name = variant and "%s (%s)" % (product.name, variant) or product.name

            # Get extra category and attribute names
            extra_categ_name = (
                product.product_extra_categ_id.name
                if product.product_extra_categ_id
                else ""
            )
            attribute_name = (
                product.product_attribute_id.name
                if product.product_attribute_id
                else ""
            )

            sellers = (
                self.env["product.supplierinfo"]
                .sudo()
                .browse(self.env.context.get("seller_id"))
                or []
            )
            if not sellers and partner_ids:
                product_supplier_info = supplier_info_by_template.get(
                    product.product_tmpl_id, []
                )
                sellers = [
                    x
                    for x in product_supplier_info
                    if x.product_id and x.product_id == product
                ]
                if not sellers:
                    sellers = [x for x in product_supplier_info if not x.product_id]
                # Filter out sellers based on the company. This is done afterwards for a better
                # code readability. At this point, only a few sellers should remain, so it should
                # not be a performance issue.
                if company_id:
                    sellers = [
                        x for x in sellers if x.company_id.id in [company_id, False]
                    ]
            if sellers:
                temp = []
                for s in sellers:
                    seller_variant = (
                        s.product_name
                        and (
                            variant
                            and "%s (%s)" % (s.product_name, variant)
                            or s.product_name
                        )
                        or False
                    )
                    temp.append(
                        get_display_name(
                            seller_variant or name,
                            s.product_code or product.default_code,
                            extra_categ_name,
                            attribute_name,
                        )
                    )
                product.display_name = ", ".join(unique(temp))
            else:
                product.display_name = get_display_name(
                    name, product.default_code, extra_categ_name, attribute_name
                )

    @api.model
    def _name_search(self, name, domain=None, operator="ilike", limit=None, order=None):
        domain = domain or []
        if name:
            positive_operators = ["=", "ilike", "=ilike", "like", "=like"]
            product_ids = []
            if operator in positive_operators:
                product_ids = list(
                    self._search(
                        [("default_code", "=", name)] + domain, limit=limit, order=order
                    )
                )
                if not product_ids:
                    product_ids = list(
                        self._search(
                            [("barcode", "=", name)] + domain, limit=limit, order=order
                        )
                    )
            if not product_ids and operator not in expression.NEGATIVE_TERM_OPERATORS:
                # Do not merge the 2 next lines into one single search, SQL search performance would be abysmal
                # on a database with thousands of matching products, due to the huge merge+unique needed for the
                # OR operator (and given the fact that the 'name' lookup results come from the ir.translation table
                # Performing a quick memory merge of ids in Python will give much better performance
                product_ids = list(
                    self._search(
                        domain + [("default_code", operator, name)],
                        limit=limit,
                        order=order,
                    )
                )
                if not limit or len(product_ids) < limit:
                    # we may underrun the limit because of dupes in the results, that's fine
                    limit2 = (limit - len(product_ids)) if limit else False
                    product2_ids = self._search(
                        domain
                        + [("name", operator, name), ("id", "not in", product_ids)],
                        limit=limit2,
                        order=order,
                    )
                    product_ids.extend(product2_ids)

                    # Add search by product_attribute_id field
                    if not limit or len(product_ids) < limit:
                        limit3 = (limit - len(product_ids)) if limit else False

                        # First, search for custom attributes that match the term
                        # Important: Set order=None to avoid using default order with priority field
                        custom_attribute_ids = self.env[
                            "product.custom.attribute"
                        ]._search(
                            [("name", operator, name)], order=None
                        )  # Removed the order parameter here

                        if custom_attribute_ids:
                            # Find products with these custom attributes
                            product3_ids = self._search(
                                [
                                    (
                                        "product_attribute_id",
                                        "in",
                                        custom_attribute_ids,
                                    ),
                                    ("id", "not in", product_ids),
                                ],
                                limit=limit3,
                                order=order,
                            )
                            product_ids.extend(product3_ids)

                        # If we're still under limit, search for products where the product_attribute_id's value matches
                        # Checking if the field exists in the model
                        if (not limit or len(product_ids) < limit) and hasattr(
                            self.env["product.custom.attribute"], "value"
                        ):
                            limit4 = (limit - len(product_ids)) if limit else False
                            product4_ids = self._search(
                                [
                                    ("product_attribute_id.value", operator, name),
                                    ("id", "not in", product_ids),
                                ],
                                limit=limit4,
                                order=order,
                            )
                            product_ids.extend(product4_ids)
            elif not product_ids and operator in expression.NEGATIVE_TERM_OPERATORS:
                domain2 = expression.OR(
                    [
                        [
                            "&",
                            ("default_code", operator, name),
                            ("name", operator, name),
                        ],
                        ["&", ("default_code", "=", False), ("name", operator, name)],
                        # Add custom attribute to negative search
                        [
                            "&",
                            ("default_code", "=", False),
                            ("product_attribute_id.name", operator, name),
                        ],
                    ]
                )

                # Only add this condition if the value field exists
                if hasattr(self.env["product.custom.attribute"], "value"):
                    domain2 = expression.OR(
                        [
                            domain2,
                            [
                                "&",
                                ("default_code", "=", False),
                                ("product_attribute_id.value", operator, name),
                            ],
                        ]
                    )

                domain2 = expression.AND([domain, domain2])
                product_ids = list(self._search(domain2, limit=limit, order=order))
            if not product_ids and operator in positive_operators:
                ptrn = re.compile(r"(\[(.*?)\])")
                res = ptrn.search(name)
                if res:
                    product_ids = list(
                        self._search(
                            [("default_code", "=", res.group(2))] + domain,
                            limit=limit,
                            order=order,
                        )
                    )
            # still no results, partner in context: search on supplier info as last hope to find something
            if not product_ids and self._context.get("partner_id"):
                suppliers_ids = self.env["product.supplierinfo"]._search(
                    [
                        ("partner_id", "=", self._context.get("partner_id")),
                        "|",
                        ("product_code", operator, name),
                        ("product_name", operator, name),
                    ]
                )
                if suppliers_ids:
                    product_ids = self._search(
                        [("product_tmpl_id.seller_ids", "in", suppliers_ids)],
                        limit=limit,
                        order=order,
                    )
        else:
            product_ids = self._search(domain, limit=limit, order=order)
        return product_ids
