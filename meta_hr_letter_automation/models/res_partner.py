import os
import base64
import tempfile
import subprocess
from fdfgen import forge_fdf

from odoo import models

def fill_pdf_with_pdftk(pdf_template_path, output_pdf_path, data_dict):
    fields = [(key, value) for key, value in data_dict.items()]
    fdf_data = forge_fdf("", fields, [], [], [])
    
    with tempfile.NamedTemporaryFile(delete=False, suffix=".fdf") as fdf_file:
        fdf_file.write(fdf_data)
        fdf_file.flush()

        subprocess.run([
            'pdftk', pdf_template_path,
            'fill_form', fdf_file.name,
            'output', output_pdf_path,
            'flatten'
        ], check=True)

    os.unlink(fdf_file.name)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    def action_generate_filled_pdf(self):
        for record in self:
            pdf_template = 'meta_hr_letter_automation/static/src/pdf/contact_form.pdf'
            output_path = f'/tmp/contact_{record.id}.pdf'

            data = {
                'FullName': record.name,
                'Email': record.email or '',
                'Phone': record.phone or '',
            }

            fill_pdf_with_pdftk(pdf_template, output_path, data)

            with open(output_path, 'rb') as f:
                content = f.read()

            self.env['ir.attachment'].create({
                'name': f'Contact_{record.name}.pdf',
                'type': 'binary',
                'datas': base64.b64encode(content),
                'res_model': 'res.partner',
                'res_id': record.id,
                'mimetype': 'application/pdf',
            })
            os.remove(output_path)
        return True
