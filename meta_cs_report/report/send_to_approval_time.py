from odoo import _, api, fields, models

class Comparison(models.Model):
    _inherit = 'comparison'  # Keep this if you're extending an existing model

    def get_send_to_approval_date(self):
        for message in self.message_ids:

            tracking_values = self.env['mail.tracking.value'].sudo().search([
                ('mail_message_id', '=', message.id),
                ('field_id.name', '=', 'state')
            ])
            
            for tracking in tracking_values:
                if tracking.old_value_char == 'User' and tracking.new_value_char == 'Technical Member':       
                    return message.date
        
        
        return False
    
    def get_cfo_approver(self):
        self.ensure_one()
        return (
            self.env["hr.employee"]
            .sudo()
            .search([("job_id.name", "=", "Senior Director, Legal and CFO")], limit=1)
        )
    
    def get_procurement_member(self):
        self.ensure_one()
        return (
            self.env["hr.employee"]
            .sudo()
            .search([("name", "ilike", "Golam Forhad")], limit=1)
        )
     
    def get_procurement_head(self):
        self.ensure_one()
        return (
            self.env["hr.employee"]
            .sudo()
            .search([("name", "ilike", "<PERSON><PERSON><PERSON>")], limit=1)
        )
    
    def get_cfo(self):
        self.ensure_one()
        return (
            self.env["hr.employee"]
            .sudo()
            .search([("name", "ilike", "Muhammed Shameem Reza")], limit=1)
        )
     