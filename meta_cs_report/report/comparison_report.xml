<odoo>
    <data>
        <template id="purchase_comparison_report" name="Purchase Comparison Report">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <!-- <t t-call="web.external_layout"> -->
                        <div class="header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <t t-set="company" t-value="env.company" />
                            <!-- Left side - Company Logo -->
                            <div class="row">
                                <div class="col-6">
                                <img t-att-src="image_data_uri(company.logo)" alt="Company Logo" style="max-height: 60px; max-width: auto;" />
                            </div>
                                <div class="col-6 mt-10" style="text-align:right;">
                                <div t-field="company.name" style="font-weight: bold; font-size: 14px;" />
                                <div t-field="company.street" style="font-size: 12px;" />
                                <div t-field="company.street2" style="font-size: 12px;" t-if="company.street2" />
                                <div style="font-size: 12px;">
                                    <span t-field="company.zip" />
                                    <span t-field="company.city" />
                                </div>
                                <div style="font-size: 12px;">
                                    <span t-field="company.state_id" />
                                    <span t-field="company.country_id" />
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class="page">
                            <!-- <div class="row" style="margin-bottom: 4px;">
                            <div class="row" style="text-align:center;">
                                <Strong>Comparison Statement: </Strong>
                                <t t-esc="o.name" />
                            </div>
                        </div>
                        <br /> -->
                            <style>
                                table tr th, table tr td {
                                    padding: 5px !important;
                                }
                                                                                                <!-- table tr {
                                    margin: 5px !important;
                                } -->
                            </style>


                            <div class="row">
                                <div class="col-12">
                                    <!-- <table class="table table-bordered table-sm o_main_table" style="width: 100% !important; border: 1px solid black !important;
                                border-collapse: collapse !important;">
                                    <thead>
                                        <tr>
                                            <th width="20%" style="text-align:center; border: 1px solid black !important;
                                font-size:11px;">Comparative Statement (CS)</th>
                                            <th width="5%" style="border: 1px solid black !important; font-size:11px;">Project</th>
                                            <th width="75%" colspan="2" style="border: 1px solid black !important; font-size:11px;">Purchase
                                Reference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <td width="20%" style="border: 1px solid black !important; font-size:11px;">
                                            CS No:<t t-esc="o.name"/>
                                            Date: <t t-esc="o.date"/>

                                    </td>
                                    <td width="5%">

                                    </td>
                                    <td width="45%" style="border: 1px solid black !important; font-size:11px;">
                                        <t t-esc="o.purchase_request_id.name"/>
                                                    Date: <t t-esc="o.purchase_request_id.date_start"/>
                                    </td>
                                    <td width="30%" style="border: 1px solid black !important; font-size:11px;">
                                                    Qoutation Date: <t t-esc="o.purchase_request_id.date_start"/>

                                    </td>
                                </tbody>
                            </table> -->
                                    <table class="table table-bordered table-sm o_main_table" style="width: 100% !important; border: 1px solid black !important; border-collapse: collapse !important;">
                                        <thead>
                                            <tr>
                                                <!-- Calculate total vendors -->
                                                <t t-set="vendor_count" t-value="len(o.requisition_id.purchase_ids.filtered(lambda p: p.state == 'draft'))"/>

                                                <!-- Calculate total columns in the bottom row (5 fixed + 2*vendors) -->
                                                <t t-set="total_columns" t-value="5 + (2 * vendor_count)"/>

                                                <!-- Distribute the 5 header items evenly -->
                                                <t t-set="col_each" t-value="int(total_columns / 5)"/>
                                                <t t-set="col_remainder" t-value="total_columns % 5"/>

                                                <th t-att-colspan="col_each + (1 if col_remainder > 0 else 0)" style="border: 1px solid black !important; font-size:11px; text-align:left; ">Comaparative Statement (CS)</th>
                                                <th t-att-colspan="col_each + (1 if col_remainder > 1 else 0)" style="border: 1px solid black !important; font-size:11px; text-align:left;">CS Number: <t t-esc="o.name"/>
                                                </th>
                                                <th t-att-colspan="col_each + (1 if col_remainder > 2 else 0)" style="border: 1px solid black !important; font-size:11px; text-align:left;">CS Date: <t t-esc="o.get_send_to_approval_date()" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                                                </th>
                                                <th t-att-colspan="col_each + (1 if col_remainder > 3 else 0)" style="border: 1px solid black !important; font-size:11px; text-align:left;">Project Name:</th>
                                                <th t-att-colspan="col_each" style="border: 1px solid black !important; font-size:11px;text-align:left;">PR Ref: <t t-esc="o.purchase_request_id.name"/>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th rowspan="2" width="5%" style="border: 1px solid black !important; font-size:11px;">Sl #</th>
                                                <th rowspan="2" width="15%" style="border: 1px solid black !important; font-size:11px;">Product/Service Name</th>
                                                <th rowspan="2" width="15%" style="border: 1px solid black !important; font-size:11px;">Strength/Specifications</th>
                                                <th rowspan="2" width="5%" style="border: 1px solid black !important; font-size:11px;">Unit</th>
                                                <th rowspan="2" width="5%" style="border: 1px solid black !important; font-size:11px;">Qty</th>
                                                <t t-foreach="o.requisition_id.purchase_ids.filtered(lambda p: p.state == 'draft').sorted(key=lambda p: p.amount_untaxed)" t-as="purchase">
                                                    <td colspan="2" style="border: 1px solid black !important; font-size:11px; text-align: center;">
                                                        <strong t-esc="purchase.partner_id.name" />
                                                    </td>
                                                </t>
                                            </tr>
                                            <tr>
                                                <t t-foreach="o.requisition_id.purchase_ids.filtered(lambda p: p.state == 'draft')" t-as="purchase">
                                                    <!-- <t t-set="is_selected" t-value="o.select_product_ids.filtered(lambda x: x.marking_product and x.purchase_order.id ==
                                                purchase.id)" /> -->
                                                    <th width="8%" t-attf-style="border: 1px solid black !important; font-size:11px; #{is_selected and 'background-color: #e6ffe6;' or ''}">Unit
                                                    Price</th>
                                                    <th width="8%" style="border: 1px solid black !important; font-size:11px;">
                                                    Total Price</th>
                                                </t>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-set="sl_no" t-value="1" />
                                            <t t-set="draft_pos" t-value="o.requisition_id.purchase_ids.sorted(key=lambda p: p.amount_untaxed).filtered(lambda p: p.state == 'draft')" />
                                            <t t-set="unique_products" t-value="draft_pos.mapped('order_line').mapped('product_id')" />

                                            <t t-foreach="unique_products" t-as="product">
                                                <tr>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: center;">
                                                        <t t-esc="sl_no" />
                                                    </td>
                                                    <t t-set="first_line" t-value="draft_pos.mapped('order_line').filtered(lambda l: l.product_id == product)[0]" />
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: left;">
                                                        <t t-esc="first_line.product_id.product_tmpl_id.name" />
                                                    </td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: left;">
                                                        <t t-esc="first_line.product_id.product_attribute_id.name" />
                                                    </td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: center;">
                                                        <span t-field="first_line.product_uom.name" />
                                                    </td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: center;">
                                                        <span t-field="first_line.product_qty" />
                                                    </td>

                                                    <t t-foreach="draft_pos" t-as="purchase">
                                                        <!-- Get PO line for current product -->
                                                        <t t-set="po_line" t-value="purchase.order_line.filtered(lambda l: l.product_id.id == product.id)" />
                                                        <!-- Check if this specific line is selected -->
                                                        <t t-set="is_selected" t-value="o.select_product_ids.filtered(lambda x: x.marking_product and x.purchase_order.id == purchase.id and x.product_line_id == po_line.id)" />
                                                        <!-- <td>
                                                        <t t-esc="is_selected" />
                                                    </td> -->
                                                        <td width="auto" t-attf-style="border: 1px solid black !important; font-size:11px; text-align: right; #{is_selected and 'background-color: #e6ffe6;' or ''}">
                                                            <t t-if="po_line">
                                                                <t t-esc="po_line[0].price_unit" t-options='{"widget": "float", "precision": 2}' />
                                                            </t>
                                                            <t t-else="">-</t>
                                                        </td>
                                                        <td width="auto" t-attf-style="border: 1px solid black !important; font-size:11px; text-align: right; #{is_selected and 'background-color: #e6ffe6;' or ''}">
                                                            <t t-if="po_line">
                                                                <t t-esc="po_line[0].price_subtotal" t-options='{"widget": "float", "precision": 2}' />
                                                            </t>
                                                            <t t-else="">-</t>
                                                        </td>
                                                    </t>
                                                </tr>
                                                <t t-set="sl_no" t-value="sl_no + 1" />
                                            </t>



                                            <t t-set="sorted_pos" t-value="o.requisition_id.purchase_ids.filtered(lambda p: p.state == 'draft').sorted(key=lambda p: p.amount_untaxed)" />

                                            <tr>
                                                <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                    <strong>Subtotal</strong>
                                                </td>
                                                <t t-foreach="sorted_pos" t-as="purchase">
                                                    <t t-if="purchase.state == 'draft'">
                                                        <!-- <t t-set="is_selected" t-value="o.select_product_ids.filtered(lambda x: x.marking_product and x.purchase_order.id == purchase.id)" /> -->
                                                        <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                        <td style="border: 1px solid black !important; font-size:11px; text-align: right;}">
                                                            <strong t-esc="purchase.amount_untaxed" t-options='{"widget": "float", "precision": 2}' />
                                                        </td>
                                                    </t>
                                                </t>
                                            </tr>
                                            <tr>
                                                <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                    <span>Add VAT &amp; AIT</span>
                                                </td>
                                                <t t-foreach="sorted_pos" t-as="purchase">
                                                    <t t-if="purchase.state == 'draft'">
                                                        <!-- Check if this vendor is selected -->
                                                        <!-- <t t-set="is_selected" t-value="o.select_product_ids.filtered(lambda x: x.marking_product and x.purchase_order.id == purchase.id)" /> -->
                                                        <td style="border: 1px solid black !important; font-size:11px; text-align: right;}">
                                                            <!-- Get all unique taxes from all order
                                                        lines -->
                                                            <t t-set="all_taxes" t-value="purchase.order_line.mapped('taxes_id')" />
                                                            <t t-foreach="all_taxes" t-as="tax">
                                                                <t t-esc="tax.amount" />
 %                                                                <br />
                                                            </t>
                                                        </td>
                                                        <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                            <t t-esc="purchase.amount_tax" t-options='{"widget": "float", "precision": 2}' />
                                                        </td>
                                                    </t>
                                                </t>
                                            </tr>
                                            <tr>
                                                <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                    <strong>Grand Total</strong>
                                                </td>
                                                <t t-foreach="sorted_pos" t-as="purchase">
                                                    <t t-if="purchase.state == 'draft'">
                                                        <!-- Check if this vendor is selected -->
                                                        <!-- <t t-set="is_selected" t-value="o.select_product_ids.filtered(lambda x: x.marking_product and x.purchase_order.id == purchase.id)" /> -->
                                                        <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                        <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                            <strong t-esc="purchase.amount_total" t-options='{"widget": "float", "precision": 2}' />
                                                        </td>
                                                    </t>
                                                </t>
                                            </tr>
                                            <tr >
                                                <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                    <strong>Vendor Status</strong>
                                                </td>
                                                <t t-set="rank" t-value="1"/>
                                                <t t-foreach="sorted_pos" t-as="purchase">
                                                    <t t-if="purchase.state == 'draft'">

                                                        <!-- <td width="8%" style="border: 1px solid black !important; font-size:11px; text-align: center;">
                                                       
                                                    </td> -->
                                                        <td colspan="2" style="border: 1px solid black !important; font-size:11px; text-align: center;">
                                                            <strong>
                                                                <t t-if="rank == 1">1st Lowest</t>
                                                                <t t-elif="rank == 2">2nd Lowest</t>
                                                                <t t-elif="rank == 3">3rd Lowest</t>
                                                                <t t-else="">
                                                                    <t t-esc="rank"/>th Lowest</t>
                                                            </strong>
                                                            <t t-set="rank" t-value="rank + 1" />
                                                        </td>
                                                    </t>
                                                </t>
                                            </tr>
                                            <!-- <tr>
                                            <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                <span>Warranty</span>
                                            </td>
                                            <t t-foreach="sorted_pos" t-as="purchase">
                                                <t t-if="purchase.state == 'draft'">
                                                    
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                        <span t-esc="purchase.warranty" />
                                                    </td>
                                                </t>
                                            </t>
                                        </tr>
                                        <tr>
                                            <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                <span>Payment Terms</span>
                                            </td>
                                            <t t-foreach="sorted_pos" t-as="purchase">
                                                <t t-if="purchase.state == 'draft'">
                                                   
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                        <span t-esc="purchase.po_payment_term.name" />
                                                    </td>
                                                </t>
                                            </t>
                                        </tr>
                                        <tr>
                                            <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                <span>Advance %</span>
                                            </td>
                                            <t t-foreach="sorted_pos" t-as="purchase">
                                                <t t-if="purchase.state == 'draft'">
                                                   
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                        <span t-esc="purchase.po_advance" t-options='{"widget": "float", "precision": 2}' />
                                                    </td>
                                                </t>
                                            </t>
                                        </tr>

                                        <tr>
                                            <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                <span>Delivery Schedule</span>
                                            </td>
                                            <t t-foreach="sorted_pos" t-as="purchase">
                                                <t t-if="purchase.state == 'draft'">
                                                   
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                        <span t-esc="purchase.delivery_schedule" />
                                                    </td>
                                                </t>
                                            </t>
                                        </tr>
                                        <tr>
                                            <td colspan="5" style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                <span>Comments</span>
                                            </td>
                                            <t t-foreach="sorted_pos" t-as="purchase">
                                                <t t-if="purchase.state == 'draft'">
                                                   
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;"></td>
                                                    <td style="border: 1px solid black !important; font-size:11px; text-align: right;">
                                                        <span t-esc="purchase.po_comments" />
                                                    </td>
                                                </t>
                                            </t>
                                        </tr> -->
                                            <!-- <tr>
                                        <td colspan="100%" style="border: 1px solid black !important; font-size:11px; text-align: left;">
                                            <strong>Selected Vendor</strong>
                                            <t t-foreach="o.select_product_ids.filtered(lambda x: x.marking_product)" t-as="purchase">
                                                <t t-esc="purchase.purchase_order.partner_id.name"/>
                                                <t t-if="not purchase_last">, </t>
                                            </t>
                                        </td>
                                    </tr> -->
                                            <tr>
                                                <td colspan="100%" style="border: 1px solid black !important; font-size:11px; text-align: left; padding:5px;">
                                                    <strong>
                                                        <u>Justification/ Note of Award: </u>
                                                    </strong>

                                                    <t t-esc="o.comments" />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!-- <table style="width: 100%; margin-top: 30px; page-break-inside: avoid;">
                                <tr>
                                    <td width="20%" style="text-align: center; padding: 10px;">
                                        <div style="min-height: 80px; margin-bottom: 5px;">
                                            <img t-if="o.technical_user_sign" t-att-src="image_data_uri(o.technical_user_sign)" alt="Digital Signature" style="max-height: 60px; max-width: 120px;" />
                                        </div>
                                        <t t-if="o.technical_user">
                                            <div style="border-top: 1px solid black; padding-top: 5px;">
                                                <p style="font-size:11px; margin: 0;" t-esc="o.technical_user.name" />
                                                <p style="font-size:11px; margin: 0;" t-esc="o.technical_user.employee_id.job_id.name" />
                                            </div>
                                        </t>

                                    </td>
                                    <td width="20%" style="text-align: center; padding: 10px;">
                                        <div style="min-height: 80px; margin-bottom: 5px;">
                                            <img t-if="o.department_user_sign" t-att-src="image_data_uri(o.department_user_sign)" alt="Digital Signature" style="max-height: 60px; max-width: 120px;" />
                                        </div>
                                        <t t-if="o.department_user">
                                            <div style="border-top: 1px solid black; padding-top: 5px;">
                                                <p style="font-size:11px; margin: 0;" t-esc="o.department_user.name" />
                                                <p style="font-size:11px; margin: 0;" t-esc="o.department_user.employee_id.job_id.name" />
                                            </div>
                                        </t>

                                    </td>
                                    <td width="20%" style="text-align: center; padding: 10px;">
                                        <div style="min-height: 80px; margin-bottom: 5px;">
                                            <img t-if="o.procurement_user_sign" t-att-src="image_data_uri(o.procurement_user_sign)" alt="Digital Signature" style="max-height: 60px; max-width: 120px;" />
                                        </div>
                                        <t t-if="o.procurement_user">
                                            <div style="border-top: 1px solid black; padding-top: 5px;">
                                                <p style="font-size:11px; margin: 0;" t-esc="o.procurement_user.name" />
                                                <p style="font-size:11px; margin: 0;" t-esc="o.procurement_user.employee_id.job_id.name" />
                                            </div>
                                        </t>

                                    </td>
                                    <td width="20%" style="text-align: center; padding: 10px;">
                                        <div style="min-height: 80px; margin-bottom: 5px;">
                                            <img t-if="o.hod_user_sign" t-att-src="image_data_uri(o.hod_user_sign)" alt="Digital Signature" style="max-height: 60px; max-width: 120px;" />
                                        </div>
                                        <t t-if="o.hod_user">
                                            <div style="border-top: 1px solid black; padding-top: 5px;">
                                                <p style="font-size:11px; margin: 0;" t-esc="o.hod_user.name" />
                                                <p style="font-size:11px; margin: 0;" t-esc="o.hod_user.employee_id.job_id.name" />
                                            </div>
                                        </t>

                                    </td>
                                    <td width="20%" style="text-align: center; padding: 10px;">
                                        <div style="min-height: 80px; margin-bottom: 5px;">
                                            <img t-if="o.cfo_user_sign" t-att-src="image_data_uri(o.cfo_user_sign)" alt="Digital Signature" style="max-height: 60px; max-width: 120px;" />
                                        </div>
                                        <t t-if="o.cfo_user">
                                            <div style="border-top: 1px solid black; padding-top: 5px;">
                                                <p style="font-size:11px; margin: 0;" t-esc="o.cfo_user.name" />
                                                <p style="font-size:11px; margin: 0;" t-esc="o.cfo_user.employee_id.job_id.name" />
                                            </div>
                                        </t>

                                    </td>
                                </tr>
                            </table> -->
                                <table style="width: 100%; margin-top: 30px; page-break-inside: avoid;">
                                    <tr style="margin-bottom:50px; padding-bottom:50px;">
                                        <th colspan="6" style="font-size:11px;margin-bottom:50px; padding-bottom:50px; text-align:left;">Procurement Committee Member Approval</th>
                                    </tr>
                                    <tr style="height:60px;"> <td colspan="6"></td> </tr>
                                    <tr>
                                      
                                        <t t-if="o.tech_member_employess">
                                            <td width="auto" style="text-align: center; padding: 10px;">
                                                <t t-if ="o.state in ['department','domain_expert','procurement','scm_hod','cfo','project_manager','field_finance','regional_coordinator','approved','confirm']">
                                                    <span style="text-align:center; font-size:11px;">Approved</span>
                                                </t>

                                                <div style="border-top: 1px solid black; padding-top: 5px;">
                                                    <span style="font-size:11px; margin: 0; text-align:center;" t-esc="o.tech_member_employess.sudo().name" />
                                                    <br/>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.tech_member_employess.sudo().job_id.name" />
                                                    <span>, </span>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.tech_member_employess.sudo().department_id.name" />

                                                </div>
                                            </td>
                                        </t>
                                        <t t-if="o.dept_member_employess">
                                            <td width="auto" style="text-align: center; padding: 10px;">
                                                <t t-if ="o.state in ['domain_expert','procurement','scm_hod','cfo','project_manager','field_finance','regional_coordinator','approved','confirm']">
                                                    <span style="text-align:center; font-size:11px;">Approved</span>
                                                </t>

                                                <div style="border-top: 1px solid black; padding-top: 5px;">
                                                    <span style="font-size:11px; margin: 0; text-align:center;" t-esc="o.dept_member_employess.sudo().name" />
                                                    <br/>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.dept_member_employess.sudo().job_id.name" />
                                                    <span>, </span>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.dept_member_employess.sudo().department_id.name" />

                                                </div>
                                            </td>
                                        </t>

                                        <t t-if="o.domain_expert_employee">
                                            <td width="auto" style="text-align: center; padding: 10px;">
                                                <t t-if ="o.state in ['procurement','scm_hod','cfo','project_manager','field_finance','regional_coordinator','approved','confirm']">
                                                    <span style="text-align:center; font-size:11px;">Approved</span>
                                                </t>

                                                <div style="border-top: 1px solid black; padding-top: 5px;">
                                                    <span style="font-size:11px; margin: 0; text-align:center;" t-esc="o.domain_expert_employee.sudo().name" />
                                                    <br/>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.domain_expert_employee.sudo().job_id.name" />
                                                    <span>, </span>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.domain_expert_employee.sudo().department_id.name" />

                                                </div>
                                            </td>
                                        </t>
                                        <t t-if="o.get_procurement_member()">
                                            <td width="auto" style="text-align: center; padding: 10px;">
                                                <t t-if ="o.state in ['scm_hod','cfo','project_manager','field_finance','regional_coordinator','approved','confirm']">
                                                    <span style="text-align:center; font-size:11px;">Approved</span>
                                                </t>

                                                <div style="border-top: 1px solid black; padding-top: 5px;">
                                                    <span style="font-size:11px; margin: 0; text-align:center;" t-esc="o.get_procurement_member().name" />
                                                    <br/>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.get_procurement_member().job_id.name" />
                                                    <span>, </span>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.get_procurement_member().department_id.name" />

                                                </div>
                                            </td>
                                        </t>
                                        <t t-if="o.get_procurement_head()">
                                            <td width="auto" style="text-align: center; padding: 10px;">
                                                <t t-if ="o.state in ['cfo','project_manager','field_finance','regional_coordinator','approved','confirm']">
                                                    <span style="text-align:center; font-size:11px;">Approved</span>
                                                </t>

                                                <div style="border-top: 1px solid black; padding-top: 5px;">
                                                    <span style="font-size:11px; margin: 0; text-align:center;" t-esc="o.get_procurement_head().name" />
                                                    <br/>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.get_procurement_head().job_id.name" />
                                                    <span>, </span>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.get_procurement_head().department_id.name" />

                                                </div>
                                            </td>
                                        </t>
                                        <t t-if="o.get_cfo()">
                                            <td width="auto" style="text-align: center; padding: 10px;">
                                                <t t-if ="o.state in ['approved','confirm']">
                                                    <span style="text-align:center; font-size:11px;">Approved</span>
                                                </t>

                                                <div style="border-top: 1px solid black; padding-top: 5px;">
                                                    <span style="font-size:11px; margin: 0; text-align:center;" t-esc="o.get_cfo().name" />
                                                    <br/>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.get_cfo().job_id.name" />
                                                    <span>, </span>
                                                    <span style="font-size:11px; margin: 0px; padding:0px ; text-align:center;" t-esc="o.get_cfo().department_id.name" />

                                                </div>
                                            </td>
                                        </t>
                                    </tr>

                                </table>
                            </div>
                        </div>
                    </t>
                <!-- </t> -->
            </t>
        </template>

        <record id="action_purchase_comparison_report" model="ir.actions.report">
            <field name="name">Print Comparison</field>
            <field name="model">comparison</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">meta_cs_report.purchase_comparison_report</field>
            <field name="report_file">meta_cs_report.purchase_comparison_report</field>
            <field name="print_report_name">'Comparison Report'</field>
            <field name="binding_model_id" ref="meta_purchase_comparison_system.model_comparison" />
            <field name="binding_type">report</field>
        </record>
        <record id="custom_purchase_comparison_report" model="report.paperformat">
            <field name="name">Custom Purchase Comparison Report</field>
            <field name="default" eval="True" />
            <field name="format">custom</field>
            <field name="page_height">297</field>
            <field name="page_width">210</field>
            <field name="orientation">Landscape</field>
            <field name="margin_top">20</field>
            <field name="margin_bottom">18</field>
            <field name="margin_left">7</field>
            <field name="margin_right">7</field>
            <field name="header_line" eval="False" />
            <field name="header_spacing">15</field>
            <field name="dpi">90</field>
        </record>
    </data>
</odoo>