<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_project_budget_tree" model="ir.ui.view">
        <field name="name">project.budget.tree</field>
        <field name="model">project.budget</field>
        <field name="arch" type="xml">
            <tree string="Project Budget">
                <field name="project" />
                <field name="program" />
                <field name="budget_type" />
                <field name="category" />
                <field name="subcategory" />
                <field name="project_start_date" />
                <field name="project_end_date" />
                <field name="gl_code_and_name" />
                <field name="quantity" />
                <field name="unit_price" />
                <field name="pr_stage" />
                <field name="po_stage" />
                <field name="spent_gl" />
                <field name="total" />
                <field name="variance" />
                <field name="monthly_budget_sum" />
                <field name="variance_percentage" />
                <field name="company_id" />
                <field name="employee_id" />
            </tree>
        </field>
    </record>

    <record id="view_project_budget_form" model="ir.ui.view">
        <field name="name">project.budget.form</field>
        <field name="model">project.budget</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="name" />
                        <field name="display_name" invisible="1" />
                        <field name="budget_year" />
                        <field name="program" />
                        <field name="budget_type" />
                        <field name="category" />
                        <field name="subcategory" />
                        <field name="project" />
                        <field name="project_start_date" />
                        <field name="project_end_date" />
                        <field name="gl_code_and_name" />
                        <field name="job_position_id" />
                        <field name="employee_id" />
                    </group>
                    <group>
                        <field name="quantity" />
                        <field name="unit_price" />
                        <field name="total_budget" />
                        <field name="pr_stage" />
                        <field name="po_stage" />
                        <field name="available_budget" />
                        <field name="spent_gl" />
                        <field name="total" />
                        <field name="variance" />
                        <field name="variance_percentage" />
                        <field name="remarks" />
                        <field name="company_id" />
                        <field name="monthly_budget_sum" />
                    </group>
                </group>
                <notebook>
                    <page string="Monthly Budgets">
                        <field name="monthly_budget_ids" widget="one2many">
                            <tree string="Monthly Budget" editable="bottom">
                                <field name="month" />
                                <field name="amount" width="50%" />
                            </tree>
                        </field>
                    </page>
                    <page string="Purchase Request Lines" name="pr_lines">
                        <field name="purchase_request_line_ids" readonly="1">
                            <tree editable="bottom">
                                <field name="request_id"/>
                                <field name="currency_id" column_invisible="1"/>
                                <field name="date_start"/>
                                <field name="product_id"/>
                                <field name="estimated_cost"/>
                            </tree>
                        </field>
                        <group>
                            <group class="oe_subtotal_footer oe_right">
                                <field name="currency_id" invisible="1"/>
                                <label for="total_est_cost"/>
                                <field name="total_est_cost" nolabel="1" class="oe_subtotal_footer_separator" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            </group>
                        </group>

                    </page>
                </notebook>


            </form>
        </field>
    </record>

    <record id="action_project_budget" model="ir.actions.act_window">
        <field name="name">Project Budget</field>
        <field name="res_model">project.budget</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="action_project_program" model="ir.actions.act_window">
        <field name="name">Project Program</field>
        <field name="res_model">project.program</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="action_budget_type" model="ir.actions.act_window">
        <field name="name">Budget Type</field>
        <field name="res_model">budget.type</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="action_budget_category" model="ir.actions.act_window">
        <field name="name">Budget Category</field>
        <field name="res_model">budget.category</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="action_budget_subcategory" model="ir.actions.act_window">
        <field name="name">Budget SubCategory</field>
        <field name="res_model">budget.subcategory</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem name="Project Budget" id="menu_budget_root" web_icon="meta_project_budget,static/description/icon.png">
        <menuitem name="Project Budget" id="menu_project_budget" action="action_project_budget" />
        <menuitem name="Budget Tracking" id="menu_budget_tracking" action="action_project_budget" />
        <menuitem name="Configurations" id="menu_budget_configurations">
            <menuitem name="Project Program" id="menu_budget_program" action="action_project_program" />
            <menuitem name="Budget Type" id="menu_budget_type" action="action_budget_type" />
            <menuitem name="Budget Category" id="menu_budget_category" action="action_budget_category" />
            <menuitem name="Budget Subcategory" id="menu_budget_subcategory" action="action_budget_subcategory" />
        </menuitem>
    </menuitem>
</odoo>