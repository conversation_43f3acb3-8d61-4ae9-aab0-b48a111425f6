# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import datetime, date

class ProjectBudget(models.Model):
    _name = 'project.budget'
    _description = 'Project Budget'
    _rec_name = "project"

    name = fields.Char(string="Reference", required=True, readonly=True,
                       index=True, default=lambda self: _('New'))
    project = fields.Char("Budget Line")
    program = fields.Many2one("project.program", "Program")
    budget_type = fields.Many2one("budget.type", "Budget Type")
    category = fields.Many2one("budget.category", "Category")
    subcategory = fields.Many2one("budget.subcategory", "Subcategory")
    quarter = fields.Selection([
        ("q1", "Q1"),
        ("q2", "Q2"),
        ("q3", "Q3"),
        ("q4", "Q4"),
    ], string="Quarter")

    project_start_date = fields.Date("Start Date")
    project_end_date = fields.Date("End Date")
    gl_code_and_name= fields.Many2one("account.account",string="GL Code & Name")
    quantity= fields.Float("Quantity")
    unit_price = fields.Float("Unit Price")
    quarterly_budget = fields.Float("Quarterly Budget")
    pr_stage = fields.Float("PR Stage", compute="_compute_pr_stage")
    po_stage = fields.Float("PO Stage", compute="_compute_po_stage")
    spent_gl = fields.Float("Spent(GL)", compute="_compute_total")
    total = fields.Float("PR + PO + GL", compute="_compute_spent_gl")
    variance = fields.Float("Variance", compute="_compute_budget_variance")
    variance_percentage = fields.Float("Variance Percentage")
    remarks = fields.Text("Remarks")
    monthly_budget_ids = fields.One2many("monthly.budget.breakdown", "budget_id", string="Monthly Budgets")
    monthly_budget_sum = fields.Float("Monthly Budget Sum", compute="_compute_monthly_budget_sum", store=True)
    employee_id = fields.Many2one("hr.employee", string="Employee")
    company_id = fields.Many2one(
        "res.company",
        string="Company",
        default=lambda self: self.env.company,
    )
    job_position_id = fields.Many2one("hr.job", "Job Position")



    def _compute_pr_stage(self):
        for record in self:
            pr_stage = 0
            for line in record.purchase_request_line_ids:
                if line.request_id.state not in ['draft', 'rejected'] and not line.purchase_id:
                    pr_stage += line.estimated_cost
            record.pr_stage = pr_stage
    
    def _compute_po_stage(self):
        for record in self:
            po_stage = 0
            for line in record.purchase_request_line_ids:
                if line.purchase_id:
                    po_stage += line.estimated_cost
            record.po_stage = po_stage
    
    def _compute_spent_gl(self):
        for record in self:
            spent_gl = 0
            if record.gl_code_and_name:
                gl_lines = self.env['account.move.line'].search([
                    ('account_id', '=', record.gl_code_and_name.id),
                    ('date', '>=', record.project_start_date),
                    ('date', '<=', record.project_end_date),
                ])
                for line in gl_lines:
                    spent_gl += line.debit - line.credit
            record.spent_gl = spent_gl


    @api.depends("pr_stage", "po_stage", "spent_gl")
    def _compute_total(self):
        for record in self:
            record.total = record.pr_stage + record.po_stage + record.spent_gl





    @api.depends("monthly_budget_ids")
    def _compute_monthly_budget_sum(self):
        for record in self:
            sum = 0
            for monthly_budget in record.monthly_budget_ids:
                sum += monthly_budget.amount
            record.monthly_budget_sum = sum

    budget_year = fields.Selection(selection="_get_years", string="Year", required=True)

    def _get_years(self):
        year_list = []
        for y in range(datetime.now().year-20, datetime.now().year + 20):
            # year_str = str(y-1)+'-'+str(y)
            year_str = str(y)
            year_list.append((year_str, year_str))
        return year_list



    @api.model
    def create(self, vals):
        # if not vals.get('name') or vals['name'] == _('New'):
        vals['name'] = vals['budget_year'] + '-' + self.env['ir.sequence'].next_by_code('seq.proj.budget') or _('New')
        return super(ProjectBudget, self).create(vals)

    display_name = fields.Char(compute='_compute_display_name', compute_sudo=True)

    @api.depends("name")
    def _compute_display_name(self):
        for rec in self:
            dis_name = ''
            if rec.name != 'New':
                dis_name += '['+rec.name+']'
            if rec.project:
                dis_name += ' ' + rec.project
            # if rec.subcategory:
            #     dis_name += ' ' + rec.subcategory.name
            rec.display_name = dis_name
    
    
    
    @api.model
    def _name_search(self, name, domain=None, operator='ilike', limit=None, order=None):
        domain = domain or []
        if name:
            domain += ['|', '|', ('name', operator, name), ('project', operator, name), ('subcategory', operator, name)]

        return self._search(domain, limit=limit, order=order)




    @api.depends('unit_price', 'quantity')
    def _compute_total_budget(self):
        for rec in self:
            rec.total_budget = rec.unit_price * rec.quantity

    total_budget = fields.Float(compute="_compute_total_budget", string='Total Budget')




    @api.depends('po_stage')
    def _compute_available_budget(self):
        for rec in self:
            # rec.available_budget = rec.total_budget - rec.po_stage
            rec.available_budget = rec.total_budget - rec.pr_stage




    @api.depends('po_stage')
    def _compute_budget_variance(self):
        for rec in self:
            rec.variance = rec.total_budget - rec.spent_gl

            

    available_budget = fields.Float(string="Available Budget", compute="_compute_available_budget")

    purchase_request_line_ids = fields.One2many(
        'purchase.request.line', 
        'project_budget_id', 
        string="Purchase Request Lines"
    )
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, default=lambda self: self.env.company.currency_id)
    total_est_cost = fields.Float(string="Total Estimated Cost", compute="_compute_total_est_cost")
    def _compute_total_est_cost(self):
        for rec in self:
            rec.total_est_cost = sum(rec.purchase_request_line_ids.mapped('estimated_cost'))
    





class ProjectProgram(models.Model):
    _name = "project.program"
    _description = "Project Program"
    _rec_name = "name"

    name = fields.Char("Project Program")


class BudgetType(models.Model):
    _name = "budget.type"
    _description = "Budget Type"
    _rec_name = "name"

    name = fields.Char("Name")




class BudgetCategory(models.Model):
    _name = "budget.category"
    _description = "Budget Category"
    _rec_name = "name"

    name = fields.Char("Name")




class BudgetSubCategory(models.Model):
    _name = "budget.subcategory"
    _description = "Budget Subcategory"
    _rec_name = "name"

    name = fields.Char("Name")

class MonthlyBudget(models.Model):
    _name = "monthly.budget.breakdown"

    budget_id = fields.Many2one("project.budget", string="Parent Budget")
    month = fields.Selection(
        [
            ("01", "January"),
            ("02", "February"),
            ("03", "March"),
            ("04", "April"),
            ("05", "May"),
            ("06", "June"),
            ("07", "July"),
            ("08", "August"),
            ("09", "September"),
            ("10", "October"),
            ("11", "November"),
            ("12", "December"),
        ],
        string="Budget Month",
        required=True,
    )
    amount = fields.Float(string="Budget for the Month")






class PurchaseRequestLine(models.Model):
    _inherit = 'purchase.request.line'

    request_id = fields.Many2one(
        comodel_name="purchase.request",
        string="Purchase Request",
        ondelete="cascade",
        readonly=True,
        index=True,
        auto_join=True,
    )
    project_budget_id = fields.Many2one('project.budget', string="Budget Line", domain="budget_domain", required=True)

    @api.depends('request_id.company_id')
    def _compute_budget_domain(self):
        for record in self:
            if record.request_id.company_id:
                record.budget_domain = f"[('company_id', '=', {record.request_id.company_id.id})]"
            else:
                record.budget_domain = "[('id', '=', False)]"

    budget_domain = fields.Char(compute='_compute_budget_domain')
    
    # project_budget_id = fields.Many2one(
    #     'project.budget', 
    #     string="Budget Line",
    #     domain="budget_domain"
    # )

    # @api.onchange('project_budget_id')
    # def get_budget_amount(self):
    #     for rec in self:
    #         if rec.project_budget_id:
    #             qty = rec.project_budget_id.quantity
    #             unit_price = rec.project_budget_id.unit_price
    #             rec.budget_cost = unit_price * qty
    #         else:
    #             rec.budget_cost = 0.00

    budget_cost = fields.Float('Available Budget', related='project_budget_id.available_budget', readonly=True)