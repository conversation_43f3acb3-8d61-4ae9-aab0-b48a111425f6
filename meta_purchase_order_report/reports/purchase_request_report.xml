<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="report_purchase_request_document_inherit" inherit_id="purchase_request.report_purchase_request">
        <xpath expr="//t[@t-call='web.external_layout']" position="replace">
            <t t-call="web.external_layout">
                <div class="page">
                    <style>
                        table {
                            border-collapse: collapse !important;
                            width: 100%;
                        }
                        table, th, td {
                            border: 1px solid black !important;
                        }
                        .no-border-spacing {
                            border-spacing: 0 !important;
                        }
                        th, td {
                            padding: 4px !important;
                        }
                        
                    </style>
                    <!-- <div class="row">
                        <table style="border: none !important;">
                            <tr>
                                <td style="text-align:left; border: none !important;">
                                    <t t-set="company" t-value="env.company"/>
                                    <img t-att-src="image_data_uri(company.logo)" alt="Company Logo" style="max-height: 70px; max-width: 70%;"/>
                                </td>
                                <td style="text-align:right; border: none !important;">
                                    <span>
                                        <b>
                                            <u>PR Received:</u>
                                        </b>
                                    </span>
                                    <br/>
                                    <span>
                                        <span>Date: <t t-esc="o.date_start" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                                        </span>
                                    </span>
    
                                    <br/>
                                    <span>
                                        <span>Time: <t t-esc="o.create_date.strftime('%H:%M:%S')" t-if="o.create_date"/>
                                        </span>
                                    </span>
                                </td>
                            </tr>
                        </table>
    
                    </div> -->
    
                    <table class="table table-bordered table-sm o_main_table no-border-spacing" style="width: 100%; margin-bottom: 0px; margin-top: -40px;">
                        <tr>
                            <td colspan="2" class="text-center" rowspan="3" style="vertical-align: middle;">PURCHASE REQUISITION (PR)</td>
                            <td style="text-align: left;">To Be Filled Out By Procurement &amp; Supply</td>
                        </tr>
                        <tr>
                            <th style="text-align: left;">PR No: <t t-esc="o.name"/>
                            </th>
                        </tr>
                        <tr>
                            <td style="text-align: left;">Expected Date of Delivery: <t t-esc="o.pr_delivery_date" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" class="text-center">To be Filled Out By Requester (Sector/ Central Services)</td>
                            <td style="text-align: left;">Budget Clearance (To Be Filled Out By Finance)</td>
                        </tr>
                        <tr>
                            <th width="30%" class="text-center">Requested By</th>
                            <th width="30%" class="text-center">Authorized By</th>
                            <th width="40%" style="text-align: left;">Project: <t t-esc="o.company_id.name"/>
                            </th>
                        </tr>
                        <tr>
                            <td>
                                <strong>Signature:</strong>
                                <br/>
                                <strong>Name: </strong>
                                <span t-esc="o.rel_emp.sudo().name"/>
    
                                <br/>
                                <strong>Designation: </strong>
                                <span t-esc="o.rel_emp.sudo().job_id.name"/>
    
                                <br/>
                                <strong>Email: </strong>
                                <t t-esc="o.rel_emp.sudo().work_email"/>
    
                                <br/>
                                <strong>Date: </strong>
                                <span t-esc="o.get_requester_date()" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
    
                                <br/>
                            </td>
                            <td>
                                <strong>Status: </strong>
                                <t t-if="o.state in ['coo_approve','to_approve','cfo_approve','approved','done']">
    
                                    <span>Approved</span>
                                </t>
    
                                <br/>
                                <!-- <t t-set="hod" t-value="o.get_hod_employee()"/> -->
                                <strong>Name: </strong>
                                <span t-esc="o.get_hod_employee().name"/>
    
                                <br/>
                                <strong>Designation: </strong>
                                <span t-esc="o.get_hod_employee().job_id.name"/>
    
                                <br/>
                                <strong>Email: </strong>
                                <span t-esc="o.get_hod_employee().work_email"/>
    
                                <br/>
                                <strong>Date: </strong>
                                <span t-esc="o.get_hod_approve_date()" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
    
                                <br/>
                            </td>
                            <td>
                                <strong>Status: </strong>
                                <t t-if="o.state in ['cfo_approve','approved','done']">
    
                                    <span>Approved</span>
                                </t>
    
                                <br/>
                                <strong>Project Closing Date: </strong>
                                <span t-esc="o.pr_closing_date" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
    
                                <br/>
                                <t t-set="account_head" t-value="o.account_head()"/>
                                <strong>Account Head: </strong>
                                <span t-esc="account_head"/>
    
                                <br/>
                                <strong>Name: </strong>
                                <span t-esc="o.finance_ho_id.sudo().name"/>
    
                                <br/>
                                <strong>Email: </strong>
                                <span t-esc="o.finance_ho_id.sudo().work_email"/>
    
                                <br/>
                                <strong>Designation: </strong>
                                <span t-esc="o.finance_ho_id.sudo().job_id.name"/>
    
                                <br/>
                                <strong>Date: </strong>
                                <span t-esc="o.get_finance_approve_date()" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
    
                                <br/>
                            </td>
                        </tr>
                    </table>
                    <table style="margin-top: 0px; width: 100%;">
                        <thead>
                            <tr>
                                <th style="">Sl No.</th>
                                <th style="">Name of Item with Specifications</th>
                                <th style="">Qtn.</th>
                                <th style="">Unit</th>
                                <th style=" text-align:right;">Unit Price (Tk.)</th>
                                <th style=" text-align:right;">Estimated Cost (Tk.)</th>
                                <th style="">Place of Delivery</th>
                                <th style="">Item to be used by</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t set="line_id_index" t-value="0"/>
                            <t set="length" t-value="o.get_pr_line_length()"/>
    
                            <tr t-foreach="o.line_ids" t-as="line_id">
                                <td style="text-align: center; color: black; padding: 5px; background-color: white; ">
                                    <span t-esc="line_id_index+1"/>
                                </td>
                                <td style="text-align: left; color: black; padding: 5px; background-color: white; ">
                                    <span t-field="line_id.product_id.name"/>
                                    <br/>
                                    <span t-field="line_id.product_id.product_attribute_id.name"/>
                                </td>
                                <td style="text-align: center; color: black; padding: 5px; background-color: white; ">
                                    <span t-esc="int(line_id.product_qty)"/>
                                </td>
                                <td style="text-align: center; color: black; padding: 5px; background-color: white; ">
                                    <span t-field="line_id.product_uom_id.name"/>
                                </td>
                                <td style="text-align: right; color: black; padding: 5px; background-color: white; ">
                                    <span t-field="line_id.unit_price" t-options='{"widget": "float", "precision": 2}'/>
                                </td>
                                <td style="text-align: right; color: black; padding: 5px; background-color: white; ">
                                    <span t-field="line_id.estimated_cost" t-options='{"widget": "float", "precision": 2}'/>
                                </td>
                                <!-- Only show these cells in the first row -->
                                <td t-if="line_id_index == 0" t-att-rowspan="len(o.line_ids)" style="text-align: left; color: black; padding: 5px; background-color: white; width: 10%;">
                                    <span t-field="o.place_delivery"/>
                                </td>
                                <td t-if="line_id_index == 0" t-att-rowspan="len(o.line_ids)" style="text-align: left; color: black; padding: 5px; background-color: white; width: 10%;">
                                    <span t-field="o.item_used_by"/>
                                </td>
                                <t set="line_id_index" t-value="line_id_index + 1"/>
                            </tr>
                            <tr>
                                <td colspan="5" style="text-align: right">Total Estimated Cost (Tk.)</td>
                                <td style="text-align: right" t-esc="o.estimated_cost" t-options='{"widget": "float", "precision": 2}'></td>
                                <td colspan="2" style="text-align: right"></td>
                            </tr>
                            <tr>
                                <td colspan="8">
                                    <span>
                                        <u>Special Instruction by the requester: </u>
                                        <t t-esc="o.description"/>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="8" style="text-align: center;">
                                    <b>Additional Information by Admin, IT</b>
                                </td>
    
    
                            </tr>
    
                            <tr>
                                <td colspan="8" style="text-align: center; height: 80px; padding: 25px;">
    
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" style="text-align: center;">
                                    <b>Approved by (COO)</b>
                                </td>
                                <td colspan="4" style="text-align: center;">
                                    <b>Approved by (CFO)</b>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <t t-set="coo_info" t-value="o.get_coo_approver()"/>
                                    <t t-set="coo_employee" t-value="coo_info.get('employee')"/>
    
                                    <span>Status:
                                        <t t-if="o.state in ['to_approve','cfo_approve','approved','done']">
                                            <t t-if="coo_info.get('status') == 'not_applicable'">
                                                <span>Not Applicable</span>
                                            </t>
                                            <t t-else="">
                                                <span>Approved</span>
                                            </t>
                                        </t>
                                        <t t-else="">
                                            <span></span>
                                        </t>
                                    </span>
                                    <br/>
    
                                    <span>Name:
                                        <t t-if="coo_employee">
                                            <t t-esc="coo_employee.name"/>
                                        </t>
    
                                    </span>
    
                                    <br/>
    
                                    <span>Designation:  
                                        <t t-if="coo_employee and coo_employee.job_id">
                                            <t t-esc="coo_employee.job_id.name"/>
                                        </t>
    
                                    </span>
                                    <br/>
    
                                    <span>Date:  
                                        <t t-if="coo_info.get('status') != 'not_applicable'">
                                            <t t-esc="o.get_coo_approve_date()" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                                        </t>
    
                                    </span>
                                    <br/>
                                </td>
                                <td colspan="4">
                                    <span>Status:
                                        <t t-if="o.state in ['approved','done']">
                                            <span>Approved</span>
    
                                        </t>
                                    </span>
                                    <br/>
    
                                    <span>Name:
                                        <t t-set="cfo" t-value="o.get_cfo_approver()"/>
                                        <t t-if="cfo">
                                            <t t-esc="cfo.name"/>
                                        </t>
    
                                    </span>
                                    <br/>
                                    <span>Designation: <t t-esc="cfo.sudo().job_id.name"/>
                                    </span>
                                    <br/>
                                    <span>Date: <t t-esc="o.get_cfo_approve_date()" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                                    </span>
                                    <br/>
    
                                </td>
                            </tr>
    
                        </tbody>
                    </table>
    
                </div>
            </t>
        </xpath>
    </template>
</odoo>