from odoo import api, models
from odoo.exceptions import ValidationError


class PurchaseRequest(models.Model):
    _inherit = "purchase.request"

    @api.constrains("budget_line_id", "approval_id", "pr_type")
    def _check_budget_line_in_mfr(self):
        for record in self:
            if record.pr_type == "local":
                if record.budget_line_id and record.approval_id:
                    valid_budget_ids = record.approval_id.budget_line_ids.mapped(
                        "budget_id"
                    ).ids
                    print("Valid Budget IDs:", valid_budget_ids)
                    if record.budget_line_id.id not in valid_budget_ids:
                        raise ValidationError(
                            "The selected Budget Line '%s' does not exist in the MFR's budget lines. "
                            "Please select a valid budget line from the MFR."
                            % record.budget_line_id.name
                        )
                elif record.budget_line_id and not record.approval_id:
                    raise ValidationError(
                        "Please select an MFR before choosing a budget line for local PR type."
                    )

    def account_head(self):
        self.ensure_one()
        if self.budget_line_id and "]" in self.budget_line_id.display_name:
            return self.budget_line_id.display_name.split("]", 1)[1].strip()

    def get_cfo_approver(self):
        self.ensure_one()

        for message in self.message_ids:
            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "CFO Approval"
                    and tracking.new_value_char in ["Approved", "Done"]
                ):
                    user_id = (
                        message.author_id.user_ids
                        and message.author_id.user_ids[0].id
                        or False
                    )
                    if user_id:
                        employee = (
                            self.env["hr.employee"]
                            .sudo()
                            .search([("user_id", "=", user_id)], limit=1)
                        )
                        if employee:
                            return employee

        default_cfo = (
            self.env["hr.employee"]
            .sudo()
            .search([("job_id.name", "=", "Senior Director, Legal and CFO")], limit=1)
        )

        if not default_cfo:
            default_cfo = (
                self.env["hr.employee"]
                .sudo()
                .search(
                    ["|", ("name", "ilike", "CFO"), ("job_id.name", "ilike", "CFO")],
                    limit=1,
                )
            )

        if not default_cfo:
            default_cfo = self.env["hr.employee"].sudo().search([], limit=1)

        return default_cfo

    def get_coo_approver(self):
        self.ensure_one()

        coo_approval_found = False
        
        for message in self.message_ids:
            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "COO Approval"
                    and tracking.new_value_char
                    in ["Finance Clearance", "CFO Approval", "Approved", "Done"]
                ):
                    coo_approval_found = True
                    user_id = (
                        message.author_id.user_ids
                        and message.author_id.user_ids[0].id
                        or False
                    )
                    if user_id:
                        employee = (
                            self.env["hr.employee"]
                            .sudo()
                            .search([("user_id", "=", user_id)], limit=1)
                        )
                        if employee:
                            return {
                                'employee': employee,
                                'status': 'approved'
                            }

        default_coo = (
            self.env["hr.employee"]
            .sudo()
            .search(
                [("job_id.name", "=", "Chief Operating Officer")], limit=1
            )
        )
        
        return {
            'employee': default_coo,
            'status': 'not_applicable' if not coo_approval_found else 'approved'
        }

    def get_coo_approve_date(self):
        for message in self.message_ids:

            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "COO Approval"
                    and tracking.new_value_char
                    in ["Finance Clearance", "CFO Approval", "Approved", "Done"]
                ):
                    return message.date

        return False

    def get_hod_employee(self):
        return (
            self.env["hr.employee"]
            .sudo()
            .search([("id", "=", self.hod_ho_id.id)], limit=1)
        )

    def get_requester_date(self):
        for message in self.message_ids:

            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "Draft"
                    and tracking.new_value_char == "HOD Approval"
                ):
                    return message.date

        return False

    def get_hod_approve_date(self):
        for message in self.message_ids:

            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "HOD Approval"
                    and tracking.new_value_char
                    in [
                        "COO Approval",
                        "Finance Clearance",
                        "CFO Approval",
                        "Approved",
                        "Done",
                    ]
                ):
                    return message.date

        return False

    def get_finance_approve_date(self):
        for message in self.message_ids:

            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "Finance Clearance"
                    and tracking.new_value_char in ["CFO Approval", "Approved", "Done"]
                ):
                    return message.date

        return False

    def get_cfo_approve_date(self):
        for message in self.message_ids:

            tracking_values = (
                self.env["mail.tracking.value"]
                .sudo()
                .search(
                    [
                        ("mail_message_id", "=", message.id),
                        ("field_id.name", "=", "state"),
                    ]
                )
            )

            for tracking in tracking_values:
                if (
                    tracking.old_value_char == "CFO Approval"
                    and tracking.new_value_char in ["Approved", "Done"]
                ):
                    return message.date

        return False

    def get_pr_line_length(self):
        for rec in self:
            if rec.line_ids:
                return len(rec.line_ids)


class PurchaseRequestLine(models.Model):
    _inherit = "purchase.request.line"

    @api.constrains("project_budget_id")
    def _check_budget_line_in_mfr(self):
        for record in self:
            if record.request_id.pr_type == "local":
                if record.project_budget_id and record.request_id.approval_id:
                    valid_budget_ids = record.request_id.approval_id.budget_line_ids.mapped(
                        "budget_id"
                    ).ids
                    print("Valid Budget IDs for PR Line:", valid_budget_ids)
                    if record.project_budget_id.id not in valid_budget_ids:
                        raise ValidationError(
                            "The selected Budget Line '%s' in line '%s' does not exist in the MFR's budget lines. "
                            "Please select a valid budget line from the MFR."
                            % (record.project_budget_id.name, record.product_id.name or 'Product')
                        )
                elif record.project_budget_id and not record.request_id.approval_id:
                    raise ValidationError(
                        "Please select an MFR in the Purchase Request before choosing a budget line for line '%s'."
                        % (record.product_id.name or 'Product')
                    )