from odoo import models, fields, api, _
from num2words import num2words
from odoo.tools import (
    is_html_empty,
    create_index,
)
import logging
import re


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'
    
    
    brand_name = fields.Char(string="Specifications")
    prod_attribute = fields.Char(string="Strength/Attribute", readonly=False, compute="_compute_prod_attribute")
    
    
    @api.depends('product_id')
    def _compute_prod_attribute(self):
        for line in self:
            if line.product_id:
                attribute_values = line.product_id.product_attribute_id.name
                line.prod_attribute = attribute_values
            else:
                line.prod_attribute = False


class YourModel(models.Model):
    _inherit = 'purchase.order'

    attention = fields.Many2one("res.partner", string="Attention", domain="[('id', 'in', partner_id.child_ids)]")
    rfq_date = fields.Date(string="RFQ/Quotation Date")
    # is_medicine = fields.Boolean(string="Is Medicine", default=False)

    def number_to_words(self, number):
        words = num2words(number, to='currency', lang='en')  # Get the currency words (default in "Euro")
        words = words.replace("euros", "taka")  # Replace euros with taka
        words = words.replace("euro", "taka")  # Handle both singular and plural forms
        words = words.replace("cents", "paisa")  # Replace cents with paisa
        words = words.replace("cent", "paisa")  # Handle singular form for cent
        return words.title()

    date_approve_date = fields.Date(string="Confirmation Date", compute="_compute_date_approve_date")

    def _compute_date_approve_date(self):
        for record in self:
            record.date_approve_date = record.date_approve.date() if record.date_approve else False
            
    
    
    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        for move in self:
            lang = move.partner_id.lang or self.env.user.lang
            company = move.env['res.company'].sudo().search([('id', '=', 1)])
            if not company.terms_type == 'html':
                narration = company.with_context(lang=lang).invoice_terms if not is_html_empty(company.invoice_terms) else ''
                
                if narration and move.partner_id.name:
                    # This pattern will match "XYZ Company" with optional "Limited" and variations in capitalization
                    pattern = re.compile(r'XYZ\s+Company(?:\s+Limited)?', re.IGNORECASE)
                    narration = pattern.sub(move.partner_id.name, narration)
                move.general_terms = narration or False




    @api.depends('state')
    def _compute_terms(self):
        use_invoice_terms = self.env['ir.config_parameter'].sudo().get_param('account.use_invoice_terms')
        for move in self:
            if not use_invoice_terms:
                move.general_terms = False
            else:
                lang = move.partner_id.lang or self.env.user.lang
                company = move.env['res.company'].sudo().search([('id', '=', 1)])
                if not company.terms_type == 'html':
                    narration = company.with_context(lang=lang).invoice_terms if not is_html_empty(company.invoice_terms) else ''
                    if narration and move.partner_id.name:
                        # This pattern will match "XYZ Company" with optional "Limited" and variations in capitalization
                        pattern = re.compile(r'XYZ\s+Company(?:\s+Limited)?', re.IGNORECASE)
                        narration = pattern.sub(move.partner_id.name, narration)
                else:
                    baseurl = company.get_base_url() + '/terms'
                    context = {'lang': lang}
                    narration = _('Terms & Conditions: %s', baseurl)
                    del context
                move.general_terms = narration or False


    general_terms = fields.Html(
        string='Terms and Conditions', compute="_compute_terms", store=True, readonly=False,
    )

    # notes = fields.Html('Terms and Conditions', default=lambda self: self._get_default_notes())
    notes = fields.Html('Terms and Conditions', default=lambda self: self._get_default_notes())

    NOTE_TEMPLATE = """
        <strong>Delivery Information: </strong>
        <br/>
        <strong>1. Delivery Deadline:</strong> The items must be delivered by {date}; late delivery is not acceptable under any circumstances.
        <br/>
        <strong>2. Delivery Place:</strong> The items have to be delivered to our Dhaka office, Ka-14/2A, Baridhara North Road, Kalachadpur, Dhaka-1212.
    """
    def _get_default_notes(self):
        if hasattr(self, 'date_planned') and self.date_planned:
            formatted_date = self.date_planned.strftime('%d %b %Y')
            return self.NOTE_TEMPLATE.format(date=formatted_date)
        return self.NOTE_TEMPLATE.format(date="[Date to be set]")

    @api.onchange('date_planned')
    def _onchange_date_planned(self):
        # Only update if notes still contains template or is empty
        if not self.notes or '{date}' in (self.notes or '') or '[Date to be set]' in (self.notes or ''):
            if self.date_planned:
                formatted_date = self.date_planned.strftime('%d %b %Y')
                self.notes = self.NOTE_TEMPLATE.format(date=formatted_date)
    
    

    # def _compute_notes(self):
    #     if hasattr(self, 'date_planned') and self.date_planned and not self.notes_manually_edited:
    #         formatted_date = self.date_planned.strftime('%d %b %Y')
            
    #         if self.notes:
    #             # If notes already exist, check if they follow our template structure
    #             if '{date}' in self.notes:
    #                 # If the template still has the placeholder, simply format it
    #                 self.notes = self.notes.format(date=formatted_date)
    #             else:
    #                 # Try to replace the existing date using regex
    #                 pattern = r'(delivered by )([^;]+)(;)'
    #                 self.notes = re.sub(pattern, f"\\1{formatted_date}\\3", self.notes)
    #         else:
    #             # If no notes exist, create them from the template
    #             self.notes = self.NOTE_TEMPLATE.format(date=formatted_date)
