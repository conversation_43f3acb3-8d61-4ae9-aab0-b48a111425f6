<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="purchase_order_form" model="ir.ui.view">
        <field name="name">purchase.order.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form//sheet//group//group//field[@name='partner_ref']" position="after">
                <field name="attention" domain="[('parent_id', '=', partner_id)]"/>
                <!-- <field name="is_medicine" invisible="1"/> -->
                <!-- <field name="notes_manually_edited" invisible="1"/> -->
            </xpath>
            <field name="notes" position="after">
                <field name="general_terms"/>
            </field>
            <field name="date_order" position="after">
                <field name="rfq_date"/>
                <field name="date_approve_date" invisible="0"/>
            </field>
            <xpath expr="//field[@name='order_line']/tree/field[@name='name']" position="after">
                <field name="brand_name" optional="show"/>
                <field name="prod_attribute" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
