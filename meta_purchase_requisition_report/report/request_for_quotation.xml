<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="report_purchasequotation_document_inherit"
            name="Purchase Requisition Report Inherit"
            inherit_id="purchase.report_purchasequotation_document">
            <xpath expr="//t[@t-set='address']" position="replace">
                <t t-set="address" t-value="''" />
            </xpath>
            <xpath expr="//div[@class='page']/h2" position="attributes">
                <attribute name="style">display:none</attribute>
            </xpath>
            <xpath expr="//div[@class='page']/h2" position="after">
                <div class="text-center" style="margin-top: -40px;">
                    <h2>
                        Request For Quotation
                    </h2>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-6"
                        style="border: 1px solid black; padding-top: 1px; padding-bottom: 1px; padding-left: 5px;">
                        <span>
                            Date:
                        </span>
                        <span t-field="o.create_date"
                            t-options='{"widget": "date", "format": "dd/MM/yyyy"}' />
                    </div>
                    <div class="col-6 text-end"
                        style="border: 1px solid black; padding-top: 1px; padding-bottom: 1px; padding-right: 5px;">
                        <span>
                            Ref:
                        </span>
                        <span t-field="o.name" />
                    </div>
                </div>
                <br />
                <span>To,</span>
                <br />
                <span t-if="o.partner_id" t-esc="o.partner_id.name" />
                <br />
                <br />
                <strong>Subject: </strong>
                <span>Request for Quotation (RFQ) for the following products/ services</span>
                <br />
                <span>Dear concern,</span>
                <br />
                <span>You are invited to submit a bid with your price offer for the following items,
                    specifications, and
                    quantities. Quotations must be submitted by </span>
                <span t-if="o.date_order"
                    t-esc="context_timestamp(o.date_order).strftime('%d/%m/%Y')" />
                <span>. Friendship will not accept late submissions and will reject them. Please use
                    the format below when preparing your offer.</span>
            </xpath>

            <xpath expr="//table[@class='table table-sm mt-4']" position="replace">
                <table class="table table-sm mt-4">
                    <thead>
                        <tr>
                            <th>
                                <strong>SL #</strong>
                            </th>
                            <th>
                                <strong>Product Code</strong>
                            </th>
                            <th>
                                <strong>Product name &amp; Specification</strong>
                            </th>
                            <th class="text-center">
                                <strong>Qty</strong>
                            </th>
                            <th class="text-center" groups="uom.group_uom">
                                <strong>Product UoM</strong>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-set="line_index" t-value="0" />
                        <tr t-foreach="o.order_line" t-as="line_ids">
                            <td class="text-center">
                                <span t-esc="line_index + 1" />
                                <t t-set="line_index" t-value="line_index + 1" />
                            </td>
                            <td>
                                <span t-if="line_ids.product_id.code"><!--internal
                                    reference exists-->
                                    <span t-field="line_ids.product_id.code">Code</span>
                                </span>
                            </td>
                            <td>
                                <span style="font-weight:bold;" t-if="line_ids.product_id"
                                    t-field="line_ids.product_id.name" />
                                <br />
                                <span style="font-weight:bold;"
                                    t-if="line_ids.product_id.product_attribute_id"
                                    t-field="line_ids.product_id.product_attribute_id.name" />
                            </td>
                            <td class="text-end">
                                <span t-field="line_ids.product_qty">5</span>
                            </td>
                            <td class="text-center" groups="uom.group_uom">
                                <span t-field="line_ids.product_uom.name">Unit</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </xpath>


            <xpath expr="//div[@class='page']" position="inside">
                <div class="mt-4">
                    <t>
                        <strong>Thanking You,</strong>
                        <br />
                        <br />
                        <br />
                        <br />
                        <br />
                        <span>____________________________________________</span>
                        <!-- <span t-esc="o.initiator_member_employess.name"/>

                        <br/>
                        <span t-esc="o.initiator_member_employess.job_id.name"/>
,                        <span t-esc="o.initiator_member_employess.department_id.name"/> -->
                    </t>
                </div>
            </xpath>
            <xpath expr="//div[@class='page']" position="attributes">
                <attribute name="style">margin-left: 40px;</attribute>
            </xpath>

        </template>
    </data>
</odoo>