<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="report_purchaserequisition_document_inherit" name="Purchase Requisition Report Inherit" inherit_id="purchase_requisition.report_purchaserequisition_document">
            <xpath expr="//div[@class='page']/h2" position="attributes">
                <attribute name="style">display:none</attribute>
            </xpath>
            <xpath expr="//div[@class='page']" position="attributes">
                <attribute name="style">margin-left: 50px; margin-right: 25px;</attribute>
            </xpath>
            <xpath expr="//div[@class='page']/h2" position="after">
                <div class="text-center" style="margin-top: -40px;">
                    <h2>
                        Request For Quotation
                    </h2>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-6" style="border: 1px solid black; padding-top: 1px; padding-bottom: 1px; padding-left: 5px;">
                        <span>
                            Date:
                        </span>
                        <span t-field="o.ordering_date" t-options='{"widget": "date", "format": "dd/MM/yyyy"}'/>
                    </div>
                    <div class="col-6 text-end" style="border: 1px solid black; padding-top: 1px; padding-bottom: 1px; padding-right: 5px;">
                        <span>
                            Ref:
                        </span>
                        <span t-field="o.name"/>
                    </div>
                </div>
                <br/>
                <span>To,</span>
                <br/>
                <span>Vendors</span>
                <br/>
                <br/>
                <strong>Subject: </strong>
                <span>Request for Quotation (RFQ) for the following products/ services</span>
                <br/>
            </xpath>
            <xpath expr="//div[@class='row my-2']" position="attributes">
                <attribute name="style">display:none</attribute>

            </xpath>

            <xpath expr="//div[@class='row my-2']" position="after">
                <div t-if="o.origin" class="col-3">
                    <strong>Source:</strong>
                    <br/>
                    <span t-field="o.origin">Origin</span>
                </div>
            </xpath>

            <xpath expr="//table[@class='table table-sm']" position="replace">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th><strong>SL #</strong></th>
                            <th><strong>Product Code</strong></th>
                            <th><strong>Product name &amp; Specification</strong></th>
                            <th class="text-end"><strong>Qty</strong></th>
                            <th class="text-center" groups="uom.group_uom">
                                <strong>Product UoM</strong>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-set="line_index" t-value="0"/>
                        <tr t-foreach="o.line_ids" t-as="line_ids">
                            <td class="text-center">
                                <span t-esc="line_index + 1"/>
                                <t t-set="line_index" t-value="line_index + 1"/>
                            </td>
                            <td>
                                <span t-if="line_ids.product_id.code"><!--internal reference exists-->
                                    <span t-field="line_ids.product_id.code">Code</span>
                                </span>
                            </td>
                            <td>
                                <span style="font-weight:bold;" t-if="line_ids.product_id" t-field="line_ids.product_id.name"/>
                                <br/>
                                <span style="font-weight:bold;" t-if="line_ids.product_id.product_attribute_id" t-field="line_ids.product_id.product_attribute_id.name"/>
                            </td>
                            <td class="text-end">
                                <span t-field="line_ids.product_qty">5</span>
                            </td>
                            <td class="text-center" groups="uom.group_uom">
                                <span t-field="line_ids.product_uom_id.name">Unit</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </xpath>

            <xpath expr="//h3[text()='Products']" position="attributes">
                <attribute name="style">display:none</attribute>
            </xpath>
            <xpath expr="//t[@t-set='address']" position="replace">
                <t t-set="address" t-value="''"/>
            </xpath>
            <xpath expr="//h3[text()='Products']" position="after">
                <br/>
                <span>Dear concern,</span>
                <br/>
                <span>You are invited to submit a bid with your price offer for the following items, specifications, and
quantities. Quotations must be submitted by </span> <span t-if="o.date_end" t-esc="context_timestamp(o.date_end).strftime('%d/%m/%Y')"/> <span>. Friendship will not accept late submissions and will reject them. Please use the format below when preparing your offer.</span>
            </xpath>

            <xpath expr="//div[@class='page']" position="inside">
                <div class="mt-4">
                    <t t-if = "o.initiator_member_employess">
                        <strong>Thanking You,</strong>
                        <br/>
                        <br/>
                        <br/>
                        <br/>
                        <br/>
                        <span t-esc="o.initiator_member_employess.name"/>

                        <br/>
                        <span t-esc="o.initiator_member_employess.job_id.name"/>
,                        <span t-esc="o.initiator_member_employess.department_id.name"/>

                    </t>
                </div>

            </xpath>
            <xpath expr="//t[@t-if='o.purchase_ids']" position="attributes">
                <attribute name="t-if">False</attribute>
            </xpath>
        </template>
    </data>
</odoo>