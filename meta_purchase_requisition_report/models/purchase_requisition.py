from odoo import models, fields, api, _
from odoo.tools import (
    is_html_empty,
)

class PurchaseRequisition(models.Model):
    _inherit = 'purchase.requisition'
    
    
    def _get_default_terms(self):
        print("helooo..................................")
        return """
        <strong>Terms and conditions: </strong>
        
        <ol>
            <li><strong>Submission method:</strong> A non-editable (PDF) copy of the quotation must be submitted by email before the closing date. Also, the vendor can submit the hard copy quotation to our Dhaka office. No public opening will be held.</li>
            
            <li><strong>Required Documents:</strong> A copy of the tax submission must be submitted with your bid to participate in the procurement process. Otherwise, the AIT will be deducted according to government rules.</li>
            
            <li><strong>Evaluation Criteria:</strong> Friendship will evaluate valid bids based on the organization's VMF policy and procedures. Invalid bids will be rejected during the financial evaluation.</li>
            
            <li><strong>Payment Mode:</strong> Payment will be made after supplying and checking the quality of items. And upon submission of an invoice through Account Payee Cheque.</li>
            
            <li><strong>Place of delivery:</strong> The materials and installation will be at the Friendship Hospital, Ukhiya; Balukhali, Ukhiya, Cox's Bazar.</li>
            
            <li><strong>Delivery timeframe:</strong> The successful bidder will have ten (10) days to deliver the total quantity. The actual delivery lead time will depend on project demands, and it will be negotiated before a Contract is awarded.</li>
            
            <li><strong>Offer Validity:</strong> The offer's validity must be mentioned in your Quotation; the validity will be at least 30 days. Otherwise, Friendship will consider the quotation for the same.</li>
            
            <li><strong>Negotiation:</strong> Friendship has reserved the right to negotiate with any bidder about the terms of the offer and price.</li>
            
            <li><strong>Quantities:</strong> This is an estimated quantity; it could be increased or decreased. Friendship is under no circumstances bound to procure the mentioned quantities or issue an award.</li>
            
            <li><strong>Rejection of Quote:</strong> If incomplete quotes are found, Friendship may not be considered further. Also, Friendship reserves the right to terminate the RFQ or Quotation at any time during the validity period for valid reasons.</li>
            
            <li><strong>VAT & AIT:</strong> VAT and Tax will be deducted at source per the Govt. rules.</li>
        </ol>
        
        """
    
    description = fields.Html(
        string='Terms and Conditions',
        # compute= _get_default_terms,
        default=lambda self: self._get_default_terms(),
        readonly=False
    )
    
    