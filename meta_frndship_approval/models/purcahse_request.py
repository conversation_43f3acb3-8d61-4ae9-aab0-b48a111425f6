from odoo import _, api, fields, models
from odoo.exceptions import UserError
from odoo.tools import safe_eval



class PurchaseRequest(models.Model):
    _inherit = "purchase.request"

    @api.depends('company_id')
    def _compute_approval_domain(self):
        for record in self:
            domain = [('request_status', '=', 'approved')]
            if self.env.user.has_group('base.group_multi_company') and record.company_id:
                domain.append(('company_id', '=', record.company_id.id))
            elif not self.env.user.has_group('base.group_multi_company'):
                domain.append(('company_id', '=', self.env.company.id))
            record.approval_domain = domain

    approval_domain = fields.Char(compute='_compute_approval_domain')
    approval_id = fields.Many2one(
        'approval.request',
        string='MFR',
        copy=False,
        domain='approval_domain',
        store=True
    )
    # pr_type = fields.Selection([('head_office', 'Head Office'), ('local', 'Local')], string='PR Type', store=True, required="1")
    

    # local_state = fields.Selection([
    #     ("draft", "Draft"),
    #     ("manager_approve", "Manager Approval"),
    #     ("finance_approve", "Finance Approval"),
    #     ('rc_approve', 'RC Approval'),
    #     ("approved", "Approved"),
    #     ("rejected", "Rejected"),
    # ], string='Local PR Status', default='draft')

    # manager_approve_id = fields.Many2one("res.users", string="Manager Approved By")
    # manager_approve_sign = fields.Binary(
    #     related="manager_approve_id.sign_signature", string="Manager Sign"
    # )

    # finance_approve_id = fields.Many2one("res.users", string="Finance Approved By")
    # finance_approve_sign = fields.Binary(
    #     related="finance_approve_id.sign_signature", string="Finance Sign"
    # )
    # rc_approve_id = fields.Many2one("res.users", string="RC Approved By")
    # rc_approve_sign = fields.Binary(
    #     related="rc_approve_id.sign_signature", string="RC Sign"
    # )

    def send_to_manager(self):
        for rec in self:
            if not "PR-" in rec.name:
                rec.name = rec._get_default_name()
            rec.write({"local_state": "manager_approve"})

    def manager_approve(self):
        current_user = self.env.user
        for rec in self:
            if rec.hod_approve_by and rec.hod_approve_by.id == current_user.id:
                rec.write({"local_state": "finance_approve"})
            else:
                raise UserError(_("Only the assigned HOD manager can approve this request."))

    def finance_approve_button(self):
        current_user = self.env.user
        for rec in self:
            if rec.finance_user_id and rec.finance_user_id.id == current_user.id:
                rec.write({"local_state": "rc_approve"})
            else:
                raise UserError(_("Only the assigned Finance user can approve this request."))

    def rc_approve(self):
        current_user = self.env.user
        for rec in self:
            if rec.rc_user_id and rec.rc_user_id.id == current_user.id:
                rec.write({"local_state": "approved"})
            else:
                raise UserError(_("Only the assigned RC user can approve this request."))

    def action_reject(self):
        for rec in self:
            rec.write({"local_state": "rejected"})
    
    



                

