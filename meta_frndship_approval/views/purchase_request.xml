<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="emp_pr_list_view_inherit" model="ir.ui.view">
        <field name="name">emp.pr.list.view.inherit</field>
        <field name="model">purchase.request</field>
        <field name="inherit_id" ref="purchase_request.view_purchase_request_tree"/>
        <field name="arch" type="xml">
            <field name="requested_by" position="after">
                <field name="rel_emp"/>
            </field>
        </field>
    </record>


    <record id="emp_pr_form_view_inherit" model="ir.ui.view">
        <field name="name">emp_pr.form.view.inherit</field>
        <field name="model">purchase.request</field>
        <field name="inherit_id" ref="purchase_request.view_purchase_request_form"/>
        <field name="arch" type="xml">
            <field name="requested_by" position="before">
                <field name="pr_type" readonly="1"/>
            </field>
            <field name="requested_by" position="after">
                <field name="rel_emp" required="True"/>
            </field>

            <xpath expr="//field[@name='requisition_id']" position="after">
                <!-- <field name="pr_type" readonly="1"/> -->
                <!-- <field name="rel_emp"/> -->
                <field name="approval_id" required="pr_type == 'local'"/>
                <field name="approval_domain" invisible="1"/>
                
            </xpath>

            
            <xpath expr="//header" position="inside">
                <field name="local_state" invisible="True"/>
                <!-- <field name="local_state" widget="statusbar" statusbar_visible="draft,manager_approve,finance_approve,rc_approve,approved" invisible="pr_type != 'local'"/> -->
                <button name="send_to_manager" type="object" string="Send for Manager Approval" class="oe_highlight" invisible="local_state not in ('draft') or pr_type == 'head_office'"/>
                <button name="manager_approve" type="object" string="Manager Approve" class="oe_highlight" invisible="local_state not in ('manager_approve') or pr_type == 'head_office'" groups="meta_frndship_approval.purchase_request_manager_user_local"/>
                <button name="finance_approve_button" type="object" string="Finance Approve" class="oe_highlight" invisible="local_state not in ('finance_approve')  or pr_type == 'head_office'" groups="meta_frndship_approval.purchase_request_finanace_user_local"/>
                <button name="rc_approve" type="object" string="RC Approve" class="oe_highlight" invisible="local_state not in ('rc_approve') or pr_type == 'head_office'" groups="meta_frndship_approval.purchase_request_rc_user_local"/>
                <button
                        name="button_rejected"
                        invisible="local_state not in ('manager_approve') or pr_type == 'head_office'"
                        string="Reject"
                        type="object"
                        groups="meta_frndship_approval.purchase_request_manager_user_local"/>
                <button
                        name="button_rejected"
                        invisible="local_state not in ('finance_approve') or pr_type == 'head_office'"
                        string="Reject"
                        type="object"
                        groups="meta_frndship_approval.purchase_request_finanace_user_local"/>
                <button
                        name="button_rejected"
                        invisible="local_state not in ('rc_approve') or pr_type == 'head_office'"
                        string="Reject"
                        type="object"
                        groups="meta_frndship_approval.purchase_request_rc_user_local"/>

                <button
                        name="%(purchase_request.action_purchase_request_line_make_purchase_order)d"
                        invisible="pr_type == 'head_office' or local_state not in ('approved')"
                        string="Create RFQ"
                        type="action"
                        groups="meta_purchase_order_approval.purchase_approve_hod_user,meta_purchase_order_approval.purchase_send_hod_user"
                />
                <button
                        name="button_done"
                        invisible="pr_type == 'head_office' or local_state not in ('approved')"
                        string="Done"
                        type="object"
                        class="oe_highlight"
                        groups="meta_purchase_order_approval.purchase_approve_hod_user,meta_purchase_order_approval.purchase_send_hod_user"
                />
                <button
                        name="action_create_tender"
                        type="object" string="Create Tender"
                        class="oe_highlight"
                        invisible="pr_type == 'head_office' or local_state not in ('approved')"
                        groups="meta_purchase_order_approval.purchase_approve_hod_user,meta_purchase_order_approval.purchase_send_hod_user" />
            </xpath>

        </field>
    </record>

</odoo>
