from odoo import _, api, fields, models
from odoo.exceptions import UserError
from datetime import date, timedelta
from odoo.http import Controller, request, route

_STATES = [
    ("draft", "User"),
    ("technical", "Technical Member"),
    ("department", "Department Approval"),
    ("domain_expert", "Domain Expert"),
    ("procurement", "Procurement"),
    ("scm_hod", "PSS Head"),
    ("cfo", "CFO"),
    ("project_manager", "Project Manager"),
    ("field_finance", "Field Finance"),
    ("regional_coordinator", "RC"),
    ("approved", "Approved"),
    ("confirm", "Confirm"),
    ("cancel", "Canceled"),
]


class PurchaseRequisition(models.Model):
    _inherit = 'purchase.requisition'

    purchase_request = fields.Many2one(
        'purchase.request', string="Purchase Request")

    def button_comparison(self):
        # for rec in self:
        self.ensure_one()

        for rec in self:
            if rec.purchase_ids:
                for item in rec.purchase_ids:
                    for item2 in item.order_line:
                        item2.cs_status = ''
            else:
                pass

        self.env['comparison'].sudo().search([('requisition_id', '=', self.id),
                                              ('state', '=', 'cancel')]).unlink()
        if self.order_count > 0:

            product_ids = []
            res = {}
            id = []
            product_list = []
            for record in self.env['purchase.order'].sudo().search(
                    [('requisition_id', '=', self.id), ('state', '!=', 'cancel')]):

                for line in record.order_line.filtered(lambda l: not l.display_type):
                    po_product_name = ' [ ' + line.product_id.default_code + ' ] ' + line.product_id.name if line.product_id.default_code else line.product_id.name
                    line_id = line.id
                    product_line_values = {
                        'name': po_product_name,
                        'vendor_id': line.order_id.partner_id.id,
                        'unit_price': line.price_unit,
                        'purchase_order': line.order_id.id,
                        'product_line_id': line_id,
                    }
                    product_list.append((0, 0, product_line_values))

            comparison = self.env['comparison'].sudo().search(
                [('requisition_id', '=', self.id)])

            if comparison:
                raise UserError(_('''Already a Comparison is Present if you Create new Comparison, 
                                    PLease Cancel or Delete First One'''))

            if not comparison:
                # compare_ctx = dict(
                #     default_requisition_id=self.id,
                #     default_select_product_ids=product_list,
                #     default_purchase_request_id=self.purchase_request.id if self.purchase_request else False,
                # )
                cs = self.env['comparison'].sudo().create({
                    'requisition_id': self.id,
                    'type_pr_tender': self.type_pr_tender,
                    'select_product_ids': product_list,
                    'purchase_request_id': self.purchase_request.id if self.purchase_request else False
                })

                for line in self.line_ids:
                    for pr_lin in line.pr_request_line:
                        pr_lin.comparison_id = cs.id

                form_view2 = [
                    (self.env.ref('meta_purchase_comparison_system.view_comparison_form').id, 'form')]
                tree_view = [
                    (self.env.ref('meta_purchase_comparison_system.view_comparison_tree').id, 'tree')]

                return {
                    'name': 'Comparison',
                    'type': 'ir.actions.act_window',
                    'view_mode': 'tree,form',
                    'res_model': 'comparison',
                    # 'views': [(tree_view.id, 'tree')],
                    "domain": [('id', '=', cs.id)],
                    # "views": tree_view,
                }

        else:
            raise UserError(
                _('There is no Order/Quotation are create, first create order then press'))

    def view_comparison(self):
        comparison = self.env['comparison'].sudo().search(
            [('requisition_id', '=', self.id)])
        form_view2 = [
            (self.env.ref('meta_purchase_comparison_system.view_comparison_form').id, 'form')]
        compare_ctx = dict(
            default_requisition_id=self.id,
        )

        return {
            'name': 'Comparison',
            'type': 'ir.actions.act_window',
            'res_model': 'comparison',
            "domain": [('requisition_id', '=', self.id)],
            "views": form_view2,
            "res_id": comparison.id,
            'context': compare_ctx,
        }

    comparison_ids = fields.One2many(
        'comparison', 'requisition_id', string='Comparison')
    comparison_id = fields.Many2one(
        'comparison', string='Comparison', store=True)

    @api.depends('comparison_ids')
    def _compute_compare_number(self):
        for requisition in self:
            requisition.comparison_count = len(requisition.comparison_ids)

    comparison_count = fields.Integer(compute='_compute_compare_number',)

    def comparison_chart(self):
        supplier_ids = []
        product_ids = []
        values = []
        amt = []
        number = []
        supplier_id = []
        counts = 1
        for record in self.env['purchase.order'].sudo().search(
                [('requisition_id', '=', self.id), ('comparison_id', '=', False), ('state', '!=', 'cancel')]):

            # Total Amount
            amount_all = []
            tax_all = []
            for total_item_amount in record.order_line.filtered(lambda l: not l.display_type):
                currency_rate = total_item_amount.get_currency_rate()
                rate = currency_rate.get('currency_rate')
                amount_all.append(total_item_amount.product_qty *
                                  (rate * total_item_amount.price_unit))
                tax_all.append(total_item_amount.price_tax)
            # Append supplier
            total_amnt_po_line = sum(amount_all)
            total_tax_amount = sum(tax_all)
            supplier_ids.append({'supplier_id': record.partner_id.id, 'sname': record.partner_id.name,
                                 'order_no': record.name, 'vat_tax_total': '{0:,.2f}'.format(total_tax_amount),
                                 'warranty': record.warranty, 'pay_term': record.po_payment_term.name, 'advance': record.po_advance,
                                 'delivery_scheduled': record.delivery_schedule, 'comment': record.po_comments,
                                 'total_foreign': '{0:,.2f}'.format(total_amnt_po_line + total_tax_amount)})
            supplier_id.append(record.partner_id.id)
            number.append(counts)
            # Append Products and quantity
            counts += 1
            for line in record.order_line.filtered(lambda l: not l.display_type):
                if values:
                    if line.product_id.id not in product_ids:
                        product_ids.append(line.product_id.id)
                        values.append({'product_id': line.product_id.id, 'product_name': line.product_id.display_name,
                                       'price': '{0:,.2f}'.format(line.price_unit), 'uom': line.product_id.uom_po_id.name,
                                       'qty': line.product_qty})
                else:
                    product_ids.append(line.product_id.id)
                    values.append({'product_id': line.product_id.id, 'product_name': line.product_id.display_name,
                                   'price': '{0:,.2f}'.format(line.price_unit), 'uom': line.product_id.uom_po_id.name,
                                   'qty': line.product_qty})

        count = 0
        supplier_amount_total = []
        no_of_col = 2
        even_number = []
        odd_number = []
        # Append amount based on the products and supplier
        for separate_values in values:
            for suppliers in supplier_ids:
                for record in self.env['purchase.order'].sudo().search(
                        [('requisition_id', '=', self.id),
                         ('partner_id', '=', suppliers['supplier_id']), ('comparison_id', '=', False), ('state', '!=', 'cancel')]):
                    for po_line in self.env['purchase.order.line'].search(
                            [('order_id', '=', record.id), ('product_id', '=', separate_values['product_id']), ('display_type', 'not in', ('line_section', 'line_note'))]):
                        currency_rate = po_line.get_currency_rate()
                        rate = currency_rate.get('currency_rate')
                        price_unit = currency_rate.get('price_unit')
                        amt.append(
                            {'total_amount': '{0:,.2f}'.format(round((po_line.product_qty * (rate * po_line.price_unit)), 2)), 'price': '{0:,.2f}'.format(po_line.price_unit),
                             'currency': po_line.order_id.currency_id.name, 'c_rate': round(rate, 2),
                             'status': po_line.cs_status})
            print("+++++++++", amt)
            values[count]['amt'] = amt
            count += 1
            amt = []
        # Generate number to create rows and columns
        total_supplier = len(number)
        if total_supplier >= 2:
            increase_by_supplier = total_supplier * no_of_col
        else:
            increase_by_supplier = no_of_col
        if total_supplier > 1:
            total_no = range(1, increase_by_supplier + 1)
            supplier_amount_total_1 = list(range(1, increase_by_supplier + 1))
        else:
            total_no = range(1, increase_by_supplier)
            supplier_amount_total_1 = list(range(1, increase_by_supplier))
        for c_number in total_no:
            if c_number % 2 == 0:
                even_number.append(c_number)
            else:
                odd_number.append(c_number)
        for record in self.env['purchase.order'].sudo().search(
                [('requisition_id', '=', self.id), ('state', '!=', 'cancel')]):
            supplier_amount_total.append(record.amount_total)
        # Update the amount in even number position
        tcount = 1
        for i in even_number:
            supplier_amount_total_1[i - 1] = supplier_amount_total[tcount - 1]
            tcount += 1
        # Update the supplier id in odd number position
        # FIXME: showing list_index error here
        scount = 1
        for odd_no in odd_number:
            for total in total_no:
                if total == odd_no:
                    supplier_amount_total_1[odd_no - 1] = supplier_id[scount - 1]
                    scount += 1

        # print('Metamorphosis', self.id)
        return ({'prc_no': self.name, 'data': values, 'supplier': supplier_ids, 'purchase_requisition_id': self.id,
                 'number': number, 'to_no': total_no, 'column_no': even_number, 'supplier_amount_total': supplier_amount_total,
                 'supplier_amount_total_1': [], 'odd_number': odd_number})


class Comparison(models.Model):
    _name = 'comparison'
    _order = 'id desc'
    _description = 'Comparison Products'
    _inherit = ["mail.thread", "mail.activity.mixin"]

    name = fields.Char(srting="Name", required=True, readonly=True,
                       index=True, default=lambda self: _('New'))
    requisition_id = fields.Many2one(
        'purchase.requisition', string="Requisition ID")
    user_id = fields.Many2one('res.users', string='Responsible', required=False, default=lambda self: self.env.user,
                              readonly=True)
    purchase_request_id = fields.Many2one(
        'purchase.request', string='Purchase Request', readonly=True)
    
    type_pr_tender = fields.Selection(selection=[
        ('direct_tender', 'Direct Tender'),
        ('single_ho_pr', 'Normal single HO PR'),
        ('single_field_pr', 'Normal single Field PR'),
        ('multiple_ho_pr', 'Multiple HO PR'),
    ], string="Type PR/Tender")

    domain_expert_req = fields.Boolean(string="Domain Expert Req.")
    domain_expert_users = fields.Many2one('res.users', string="Domain Expert Users", tracking=True)
    domain_expert_employee = fields.Many2one('hr.employee', string="Domain Expert Member", tracking=True)

    date = fields.Date(
        string='Date',
        index=True,
        readonly=True,
        copy=False,
        default=fields.Date.context_today
    )
    company_id = fields.Many2one('res.company', string='Company', required=True, readonly=True,
                                 default=lambda self: self.env.company)
    state = fields.Selection(
        selection=_STATES,
        string="Status",
        index=True,
        tracking=True,
        required=True,
        copy=False,
        default="draft",
    )

    comments = fields.Text(string="Comments")
    select_product_ids = fields.One2many(
        'select.po.products', 'comparison', string="Comparison Products")

    def get_comparison_chart(self, **args):
        chart = self.env['comparison'].sudo().search([('id', '=', args['requisitionId'])])
        return chart.requisition_id.comparison_chart()

    @api.depends('state')
    def _confirm_po_count(self):
        for rec in self:
            if rec.state == 'confirm':
                rec.confirm_order_count = len(self.env['purchase.order'].sudo().search([('comparison_id', '=', rec.id)]))
    
            else:
                rec.confirm_order_count = 0
    
    confirm_order_count = fields.Integer(compute="_confirm_po_count")

    @api.model
    def create(self, vals):
        # if not vals.get('name') or vals['name'] == _('New'):
        vals['name'] = self.env['ir.sequence'].next_by_code(
            'seq.po.compare') or _('New')
        return super(Comparison, self).create(vals)

    def base_url(self):
        menu_id = self.env.ref(
            'meta_purchase_comparison_system.menu_comparison_act').id
        base_url = request.env['ir.config_parameter'].get_param('web.base.url')
        base_url += '/web#id=%d&view_type=form&model=%s&menu_id=%s' % (
            self.id, self._name, menu_id)

        return base_url

    @api.ondelete(at_uninstall=False)
    def _unlink_if_cancelled(self):
        for order in self:
            if not order.state == 'cancel':
                raise UserError(
                    _('In order to delete a CS, you must cancel it first.'))

    def marking_product(self):
        sub_total = []
        for line in self.select_product_ids.filtered(lambda l: l.marking_product):
            if line:
                product_line = self.env['purchase.order.line'].sudo().search(
                    [('id', '=', line.product_line_id)])
                if product_line:
                    product_line.cs_status = 'Selected'
                    sub_total.append(product_line.price_subtotal)
                else:
                    product_line.cs_status = ''

            else:
                pass
        self.total_cs_value = sum(sub_total)
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    total_cs_value = fields.Float('Total CS Value', readonly=True)

    def confirm_cs(self):
        for line in self.select_product_ids.filtered(lambda l: l.marking_product):
            if line:
                order_line = self.env['purchase.order.line'].sudo().search([('id', '=', line.product_line_id)])
        
                order = self.env['purchase.order'].sudo().search([('id', '=', order_line.order_id.id)])
                order2 = self.env['purchase.order'].sudo().search([('against_po_id', '=', str(order_line.order_id.id))])
                if not order2:
                    order = self.env['purchase.order'].create({
                        'partner_id': order.partner_id.id,
                        'partner_ref': order.partner_ref,
                        'currency_id': order.currency_id.id,
                        'requisition_id': order.requisition_id.id,
                        'against_po_id': str(order.id),
                        'comparison_id': self.id,
                        'order_line': [(0, 0, {
                            'name': order_line.product_id.name,
                            'product_id': order_line.product_id.id,
                            'is_cs_po': True,
                            'product_qty': order_line.product_qty,
                            'product_uom': order_line.product_uom.id,
                            'price_unit': order_line.price_unit,
                            'discount': order_line.discount,
                            'taxes_id': [(6, 0, order_line.taxes_id.ids)],
                        })]
                    })
        
                    order.write({'state': 'draft'})
                    # order.message_post_with_view('meta_purchase_comparison_system.track_cs_purchase_order_creation',
                    #                                 values={'self': order, 'origin': self},
                    #                                 subtype_id=self.env['ir.model.data']._xmlid_to_res_id(
                    #                                     'mail.mt_note'))
        
                elif order2:
                    self.env['purchase.order.line'].create({
                        'name': order_line.product_id.name,
                        'product_id': order_line.product_id.id,
                        'is_cs_po': True,
                        'order_id': order2.id,
                        'product_qty': order_line.product_qty,
                        'product_uom': order_line.product_uom.id,
                        'price_unit': order_line.price_unit,
                        'discount': order_line.discount,
                        'taxes_id': [(6, 0, order_line.taxes_id.ids)],
                    })
        
                else:
                    pass

        return self.write({'state': 'confirm'})

    def view_confirm_po_order(self):
        self.ensure_one()
    
        action = {
            'name': _("Requests for Quotation"),
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.order',
            'target': 'current',
        }
        # invoice_ids = self.invoice_ids.ids
        comparison = self.env['purchase.order'].sudo().search([('comparison_id', '=', self.id)])
    
        if len(comparison) == 1:
            order = comparison.id
            action['res_id'] = order
            action['view_mode'] = 'form'
            action['views'] = [(self.env.ref('purchase.purchase_order_form').id, 'form')]
        else:
            action['view_mode'] = 'tree,form'
            action['domain'] = [('id', 'in', comparison.ids)]
    
        return action

    def reset_selected_product_order(self):
        for rec in self:
            rec.select_product_ids = [(5, 0, 0)]
            rec.total_cs_value = 0.00
            product_list = []
            for line in rec.requisition_id.purchase_ids.order_line.filtered(lambda l: not l.display_type):
                line.cs_status = ''
                po_product_name = ' [ ' + line.product_id.default_code + ' ] ' + line.product_id.name if line.product_id.default_code else line.product_id.name
                line_id = line.id
                product_line_values = {
                    'name': po_product_name,
                    'vendor_id': line.order_id.partner_id.id,
                    'unit_price': line.price_unit,
                    'purchase_order': line.order_id.id,
                    'product_line_id': line_id,
                }
                product_list.append((0, 0, product_line_values))
            rec.select_product_ids = product_list

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
    #
    def cancel_confirm_cs(self):
        for rec in self:
            for item in rec.select_product_ids:
                item.marking_product = False

            for line in rec.requisition_id.purchase_ids.order_line:
                line.cs_status = ''
            comparison_confirm_po = self.env['purchase.order'].sudo().search([('comparison_id', '=', rec.id)])
            for cs in comparison_confirm_po.filtered(lambda p: p.state not in ['purchase', 'done']):
                # cs.is_locked_order = False
                cs.button_cancel()
                cs.unlink()

        return self.write({"state": "cancel"})
    
    def action_reset_draft(self):
        return self.write({"state": "draft"})

    # def send_to_technical(self):
    #     for rec in self:
    #         if rec.department_user:
    #             rec.write({'state': 'technical'})
    #         else:
    #             raise UserError(_('Please Select Department Approver Then Send to Technical'))

    # def techincal_approve(self):
    #     for rec in self:
    #         rec.technical_user = rec.env.user.id
    #         rec.write({'state': 'department'})

    # def department_approve(self):
    #     for rec in self:
    #         if rec.department_user:
    #             if rec.department_user == rec.env.user:
    #                 rec.write({"state": "procurement"})
    #             else:
    #                 raise UserError(_('You are not allowed to approve this '))

    #         else:
    #             raise UserError(_('Requester not select the Department approver, please reject or reset then inform the requester to select Department approver.'))

    #     # for rec in self:
    #     #     rec.department_user = rec.env.user.id
    #     #     rec.write({'state': 'procurement'})

    # def prcuremnet_approve(self):
    #     for rec in self:
    #         rec.procurement_user = rec.env.user.id
    #         rec.write({'state': 'scm_hod'})
    
    # def scm_hod_approve(self):
    #     for rec in self:
    #         rec.hod_user = rec.env.user.id
    #         rec.write({'state': 'cfo'})
    
    # def cfo_approve(self):
    #     for rec in self:
    #         rec.cfo_user = rec.env.user.id
    #         rec.write({'state': 'approved'})

    technical_user = fields.Many2one('res.users', string="Technical", tracking=True)
    technical_user_sign = fields.Binary(related='technical_user.sign_signature', string="Signature")
    department_user = fields.Many2one('res.users', string="Department", tracking=True)
    department_user_sign = fields.Binary(related='department_user.sign_signature', string="Signature")
    procurement_user = fields.Many2one('res.users', string="Procurement", tracking=True)
    procurement_user_sign = fields.Binary(related='procurement_user.sign_signature', string="Signature")
    hod_user = fields.Many2one('res.users', string="HOD", tracking=True)
    hod_user_sign = fields.Binary(related='hod_user.sign_signature', string="Signature")
    cfo_user = fields.Many2one('res.users', string="CFO", tracking=True)
    cfo_user_sign = fields.Binary(related='cfo_user.sign_signature', string="Signature")

    project_manager_user = fields.Many2one('res.users', string="Project Manager", tracking=True)
    project_manager_user_sign = fields.Binary(related='project_manager_user.sign_signature', string="Signature")

    field_finance_user = fields.Many2one('res.users', string="Field Finance", tracking=True)
    field_finance_user_sign = fields.Binary(related='field_finance_user.sign_signature', string="Signature")

    rc_user = fields.Many2one('res.users', string="RC", tracking=True)
    rc_user_sign = fields.Binary(related='rc_user.sign_signature', string="Signature")

    # @api.model
    # def _get_view(self, view_id=None, view_type='form', **options):
    #     arch, view = super()._get_view(view_id, view_type, **options)
    #     # if self.state == 'approved':
    #     # state = next(iter(arch.xpath('//field[@name="state"]')), None)
    #     record_id = self._context.get("active_id")
    #     print('Joyanto', record_id)
    #     if view_type == "form":
    #         # if self.state == 'approved':
    #         for node in arch.xpath("//field"):
    #             node.set("readonly", "1")
    #     return arch, view


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    comparison_id = fields.Many2one('comparison', string="CS", store=True)
    against_po_id = fields.Char('Against PO ID')

    @api.model
    def create(self, vals):
        purchase = super(PurchaseOrder, self).create(vals)
        if purchase.requisition_id.comparison_id:
            purchase.comparison_id = purchase.requisition_id.comparison_id.id
        return purchase

    @api.onchange('requisition_id')
    def get_cs_id(self):
        for rec in self:
            if rec.requisition_id.comparison_id:
                rec.comparison_id = rec.requisition_id.comparison_id.id
            else:
                rec.comparison_id = False


class SelectedProductsOreder(models.Model):
    _name = 'select.po.products'

    name = fields.Char(string='Name')
    vendor_id = fields.Many2one('res.partner', string='Vendor')
    product_line_id = fields.Integer(string='Line Id')
    purchase_order = fields.Many2one('purchase.order', string='PO No')
    unit_price = fields.Float(string='Unit Price')
    comparison = fields.Many2one('comparison', string='Comparison')
    marking_product = fields.Boolean(string="Select", default=False)


class PurchaseOrderLineInherit(models.Model):
    _inherit = 'purchase.order.line'

    cs_status = fields.Char(string='CS Status')
    is_cs_po = fields.Boolean(string='Is CS PO', default=False)

    def get_currency_rate(self):
        for po_line in self:
            currency_rate = 0.00
            price_unit = 0.00
            if po_line.order_id.company_id.currency_id != po_line.currency_id:
                company = po_line.env.company
                date = po_line.date_order or fields.Date.today()
                currency_rates = po_line.order_id.currency_id.rate_ids[0].inverse_company_rate
                currency_rate = currency_rates
            else:
                currency_rate = po_line.order_id.company_id.currency_id.rate

            return {'currency_rate': currency_rate}
