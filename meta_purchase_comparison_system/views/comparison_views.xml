<?xml version="1.0" ?>
<!-- Copyright 2018-2019 ForgeFlow, S.L.
     License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0) -->
<odoo>
    <record id="view_comparison_form" model="ir.ui.view">
        <field name="name">view.comparison.form</field>
        <field name="model">comparison</field>
        <field name="arch" type="xml">
            <form string="Comparison" create="false">
                <header>
                    <button name="cancel_confirm_cs" string="Cancel" class="oe_highlight" type="object"
                        invisible="state in ('cancel', 'approved', 'confirm', 'draft')"/>
                    <button name="action_reset_draft" string="Reset to Draft" class="oe_highlight" type="object"
                        invisible="state not in ('cancel')"/>
                    <!-- <button name="send_to_technical" type="object" string="Send for Technical Approval" class="oe_highlight" invisible="state not in ('draft')"/>
                    
                    <button name="techincal_approve" type="object" string="Approve" groups="meta_purchase_comparison_system.technical_approver_user"
                        class="oe_highlight" invisible="state not in ('technical')"/>
                    <button name="department_approve" type="object" string="Approve" groups="meta_purchase_comparison_system.department_approver_user"
                        class="oe_highlight" invisible="state not in ('department')"/>
                    <button name="prcuremnet_approve" type="object" string="Approve" groups="meta_purchase_comparison_system.procurement_approver_user"
                        class="oe_highlight" invisible="state not in ('procurement')"/>
                    <button name="scm_hod_approve" type="object" string="Approve" groups="meta_purchase_comparison_system.scm_hod_approver_user"
                        class="oe_highlight" invisible="state not in ('scm_hod')"/>
                    <button name="cfo_approve" type="object" string="Approve" groups="meta_purchase_comparison_system.cfo_approver_user"
                        lass="oe_highlight" invisible="state not in ('cfo')"/> -->
                    <button name="confirm_cs" string="Confirm" class="oe_highlight" type="object" invisible="state not in ('approved')"/>
                    <field name="state" widget="statusbar" 
                        invisible="type_pr_tender != 'single_ho_pr'"
                        statusbar_visible="draft,technical,department,domain_expert,procurement,scm_hod,cfo,approved,confirm"/>
                    <field name="state" widget="statusbar" 
                        invisible="type_pr_tender != 'single_field_pr'"
                        statusbar_visible="draft,technical,project_manager,field_finance,regional_coordinator,approved,confirm"/>
                    <field name="state" widget="statusbar" 
                        invisible="type_pr_tender != 'multiple_ho_pr'"
                        statusbar_visible="draft,procurement,scm_hod,cfo,approved,confirm"/>
                </header>
                <sheet>
                   <div class="oe_button_box" name="button_box">
                       <button name="view_confirm_po_order" type="object" class="oe_stat_button" icon="fa-list-alt"
                               invisible="state not in ('confirm') or confirm_order_count == 0">
                           <field name="confirm_order_count" widget="statinfo" string="Confirm PO"/>
                       </button>
                   </div>

                    <div class="oe_edit_only">
                        <label for="name" class="oe_inline" />
                    </div>
                    <h1>
                        <field name="name" class="oe_inline"/>
                    </h1>
                    <group>
                        <group>
                            <field name="requisition_id" readonly="1" string="Tender ID"/>
                            <field name="user_id"/>
                            <field name="domain_expert_req" readonly="state not in ('draft', 'technical', 'department', 'procurement', 'scm_hod')" invisible="type_pr_tender not in ('single_ho_pr')"/>
                            <field name="domain_expert_employee" readonly="state != 'department'" required="domain_expert_req"
                                    invisible="type_pr_tender not in ('single_ho_pr') or not domain_expert_req"/>
                            <field name="department_user" invisible="1" readonly="state not in ('draft')" 
                                    string="Dept. Approve By" />
                            <field name="company_id"/>
                            <field name="type_pr_tender" readonly="1"/>
                        </group>
                        <group>
                            <field name="total_cs_value"/>
                            <field name="purchase_request_id"/>
                            <field name="date"/>
                            <field name="comments" readonly="state in ('approved', 'confirm')"/>
                            <!-- readonly="state in ('approved', 'confirm')" -->
                            <field name="technical_user" invisible="1"/>
                            <field name="procurement_user" invisible="1"/>
                            <field name="hod_user" invisible="1"/>
                            <field name="cfo_user" invisible="1"/>
                            <field name="project_manager_user" invisible="1"/>
                            <field name="field_finance_user" invisible="1"/>
                            <field name="rc_user" invisible="1"/>
                            <field name="technical_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                            <field name="department_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                            <field name="procurement_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                            <field name="hod_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                            <field name="cfo_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>

                            <field name="project_manager_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                            <field name="field_finance_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                            <field name="rc_user_sign" widget="signature" invisible="1"
                                options="{'full_name': 'display_name', 'size': ['',200]}"/>
                        </group>
                    </group>
                    <group>
                        <field name="select_product_ids" widget="section_and_note_one2many" mode="tree" readonly="state in ('approved', 'confirm')">
                            <tree string="Product Lines" create="false" delete="false" editable="bottom">
                                <field name="vendor_id" readonly="1"/>
                                <field name="purchase_order" readonly="1"/>
                                <field name="name" string="Products" readonly="1"/>
                                <field name="unit_price" readonly="1"/>
                                <field name="marking_product"/>
                                <field name="product_line_id" column_invisible="True"/>
                                <field name="comparison" column_invisible="True"/>
                            </tree>
                        </field>
                        <group>
                            <button name="marking_product" string="Marking" class="btn-success"
                                    type="object" style="margin-left: 15px;"
                                    invisible="state in ('confirm', 'cancel')"/>

                            <button name="reset_selected_product_order" string="Reset" class="btn-success"
                                    type="object" style="margin-left: 15px;"
                                    invisible="state in ('confirm', 'cancel')"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Comparison Products">
                            <div class="purchase_comparison_summary" colspan="2"/>
                        </page>
                    </notebook>
                </sheet>

                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers" />
                    <field name="activity_ids" widget="mail_activity" />
                    <field name="message_ids" widget="mail_thread" />
                </div>

            </form>
        </field>
    </record>

    <record id="view_comparison_tree" model="ir.ui.view">
        <field name="name">view.comparison.tree</field>
        <field name="model">comparison</field>
        <field name="arch" type="xml">
            <tree string="Comparison" create="false" decoration-muted="state == 'cancel'">
                <field name="name"/>
                <field name="requisition_id" string="Tender ID"/>
                <field name="purchase_request_id" column_invisible="1"/>
                <field name="date"/>
                <field name="state" widget="badge"
                       decoration-warning="state == 'draft'"
                       decoration-success="state == 'approve'"
                       decoration-muted="state == 'cancel'"/>
            </tree>
        </field>
    </record>

    <record model="ir.actions.act_window" id="comparison_form_action">
        <field name="name">Comparison</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">comparison</field>
        <field name="view_mode">tree,form</field>
        <field name="context"></field>
        <!--<field name="search_view_id" ref="view_purchase_request_search" />-->
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                Click to start a new comparison.
            </p>
        </field>
    </record>

    <menuitem
        id="menu_comparison_act"
        name="Comparison"
        sequence="10"
        parent="purchase.menu_procurement_management"
        action="comparison_form_action"/>
</odoo>