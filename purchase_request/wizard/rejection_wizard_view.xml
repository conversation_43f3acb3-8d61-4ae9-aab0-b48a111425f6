<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_rejection_note_form" model="ir.ui.view">
        <field name="name">Rejection Note View Form</field>
        <field name="model">rejection.note.wizard</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Rejection Note">
                <sheet>
                    <group name="rejection_note" string="Rejection Note">
                        <field name="requisition_id" readonly="1"/>
                        <field name="rejecct_note" readonly="[('requisition_id.state', '!=', 'draft')]"/>
                    </group>
                </sheet>
                <footer>
                    <button
                        name="action_reject_note"
                        string="Apply"
                        type="object"
                        class="oe_highlight"
                    />
                    <button special="cancel" string="Cancel" class="oe_link" />
                </footer>
            </form>
        </field>
    </record>
</odoo>