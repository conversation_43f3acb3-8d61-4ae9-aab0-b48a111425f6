# Copyright 2018-2019 <PERSON><PERSON><PERSON><PERSON>, S.L.
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0).
from datetime import datetime

import pytz

from odoo import _, api, fields, models
from odoo.exceptions import UserError
from odoo.tools import get_lang


class RejectionNoteWizard(models.TransientModel):
    _name = 'rejection.note.wizard'
    _description = 'Rejection Note'

    requisition_id = fields.Many2one('purchase.request', string="Request ID")
    rejecct_note = fields.Text('Rejection Note')

    def action_reject_note(self):
        for rec in self:
            rec.requisition_id.mapped("line_ids").do_cancel()
            rec.requisition_id.rejection_ntoe = rec.rejecct_note
            rec.requisition_id.budget_line_id.pr_stage -= rec.requisition_id.estimated_cost
            rec.requisition_id.write({
                'state': 'rejected'
            })

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }