<?xml version="1.0"?>
<!-- Copyright 2018-2019 ForgeFlow, S.L.
     License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0) -->
<odoo>
        <record id="view_purchase_request_form" model="ir.ui.view">
                <field name="name">purchase.request.form</field>
                <field name="model">purchase.request</field>
                <field name="arch" type="xml">
                        <form string="Purchase Request">
                                <header>
                                        <button
                                                name="button_draft"
                                                string="Reset"
                                                invisible="state not in ('rejected')"
                                                type="object"
                                                groups="purchase_request.purchase_request_reset_resject_access"
                                        />
                                        <!--
                                        groups="purchase_request.group_purchase_request_manager" -->

                                        <!-- <button
                        name="button_to_approve"
                        invisible="state not in ('draft')"
                        string="Request approval"
                        type="object"
                        class="oe_highlight"
                    /> -->
                                        <button
                                                name="button_approved"
                                                invisible="state not in ('cfo_approve')"
                                                string="Approve"
                                                type="object"
                                                class="oe_highlight"
                                                groups="purchase_request.purchase_request_cfo_user"
                                        />
                                        <button
                                                name="%(action_purchase_request_line_make_purchase_order)d"
                                                invisible="state not in ('approved')"
                                                string="Create PO"
                                                type="action"
                                                groups="meta_purchase_order_approval.purchase_approve_hod_user,meta_purchase_order_approval.purchase_send_hod_user"/>
                                        <button
                                                name="button_done"
                                                invisible="state not in ('approved')"
                                                string="Done"
                                                type="object"
                                                class="oe_highlight"
                                                groups="meta_purchase_order_approval.purchase_approve_hod_user,meta_purchase_order_approval.purchase_send_hod_user"/>
                                        <button
                                                name="action_create_tender"
                                                type="object" string="Create RFQ"
                                                class="oe_highlight"
                                                invisible="pr_type == 'local' or state not in ('approved')"   
                                                groups="meta_purchase_order_approval.purchase_approve_hod_user,meta_purchase_order_approval.purchase_send_hod_user"/>
                                        <!-- For local PR -->
                                        <!-- <button
                                                name="%(action_purchase_request_line_make_purchase_order)d"
                                                invisible="pr_type == 'head_office' or local_state not in ('approved')"
                                                string="Create RFQ"
                                                type="action"
                                        />
                                        <button
                                                name="button_done"
                                                invisible="pr_type == 'head_office' or local_state not in ('approved')"
                                                string="Done"
                                                type="object"
                                                class="oe_highlight"
                                                groups="purchase_request.group_purchase_request_manager"
                                        />
                                        <button
                                                name="action_create_tender"
                                                type="object" string="Create Tender"
                                                class="oe_highlight"
                                                invisible="pr_type == 'head_office' or local_state not in ('approved')" /> -->
                                        <!-- End Local PR -->

                                        <button name="send_to_hod" type="object"
                                                string="Send for HOD Approval" class="oe_highlight"
                                                invisible="state not in ('draft') or pr_type == 'local'" />
                                        <button name="finance_approve" type="object"
                                                string="Approve" class="oe_highlight"
                                                invisible="state not in ('to_approve') or pr_type == 'local'"
                                                groups="purchase_request.purchase_request_finanace_user" />
                                        <button name="hod_approve_button" type="object"
                                                string="Approve" class="oe_highlight"
                                                invisible="state not in ('hod_approve') or pr_type == 'local'"
                                                groups="purchase_request.purchase_request_hod_user" />
                                        <button name="coo_approve" type="object" string="Approve"
                                                class="oe_highlight"
                                                invisible="state not in ('coo_approve') or pr_type == 'local'"
                                                groups="purchase_request.purchase_request_coo_user" />

                                        <!--  -->
                                        <button
                                                name="button_rejected"
                                                invisible="state not in ('hod_approve') or pr_type == 'local'"
                                                string="Reject"
                                                type="object"
                                                groups="purchase_request.purchase_request_hod_user" />
                                        <!-- groups="purchase_request.purchase_request_hod_user" -->
                                        <button
                                                name="button_rejected"
                                                invisible="state not in ('coo_approve') or pr_type == 'local'"
                                                string="Reject"
                                                type="object"
                                                groups="purchase_request.purchase_request_coo_user" />
                                        <!-- groups="purchase_request.purchase_request_coo_user" -->
                                        <button
                                                name="button_rejected"
                                                invisible="state not in ('to_approve') or pr_type == 'local'"
                                                string="Reject"
                                                type="object"
                                                groups="purchase_request.purchase_request_finanace_user" />
                                        <!--
                                        groups="purchase_request.purchase_request_finanace_user" -->
                                        <button
                                                name="button_rejected"
                                                invisible="state not in ('cfo_approve') or pr_type == 'local'"
                                                string="Reject"
                                                type="object"
                                                groups="purchase_request.purchase_request_cfo_user" />
                                        
                                        <button
                                                name="button_rejected"
                                                invisible="state not in ('approved') or pr_type == 'local'"
                                                string="Reject"
                                                type="object"
                                                groups="purchase_request.purchase_request_reset_resject_access"/>

                                        <!-- groups="purchase_request.purchase_request_cfo_user" -->
                                        <!-- <button name="cfo_approve" type="object"
                                        string="Approve" class="oe_highlight" invisible="state not
                                        in ('coo_approve')"/> -->
                                        <field name="rel_emp_domain" invisible="1" />
                                        <field name="pr_type" invisible="1" />
                                        <field
                                                name="state"
                                                widget="statusbar"
                                                statusbar_visible="draft,to_approve,hod_approve,coo_approve,cfo_approve,approved"
                                                statusbar_colors='{"approved":"blue"}'
                                                invisible="pr_type != 'head_office'"
                                        />
                                        <field
                                                name="local_state"
                                                widget="statusbar"
                                                statusbar_visible="draft,manager_approve,finance_approve,rc_approve,approved"
                                                statusbar_colors='{"approved":"blue"}'
                                                invisible="pr_type != 'local'"
                                        />
                                </header>
                                <sheet>
                                        <div class="oe_button_box" name="button_box">
                                                <button
                                                        type="object"
                                                        name="action_view_purchase_request_line"
                                                        class="oe_stat_button"
                                                        invisible="line_count == 0"
                                                        icon="fa-list"
                                                >
                                                        <field name="line_count" widget="statinfo"
                                                                string="Lines" />
                                                </button>
                                                <button
                                                        type="object"
                                                        name="action_view_purchase_order"
                                                        class="oe_stat_button"
                                                        invisible="purchase_count == 0"
                                                        icon="fa-shopping-cart"
                                                        string="Purchase Order"
                                                >
                                                        <field
                                                                name="purchase_count"
                                                                widget="statinfo"
                                                                string="Purchase Orders"
                                                        />
                                                </button>
                                                <button
                                                        type="object"
                                                        name="action_view_stock_picking"
                                                        class="oe_stat_button"
                                                        invisible="move_count == 0"
                                                        groups="stock.group_stock_manager"
                                                        icon="fa-truck"
                                                        string="Picking"
                                                >
                                                        <field
                                                                name="move_count"
                                                                widget="statinfo"
                                                                string="Pickings"
                                                        />
                                                </button>
                                        </div>
                                        <div class="oe_edit_only">
                                                <label for="name" class="oe_inline" />
                                        </div>
                                        <h1>
                                                <field name="is_editable" invisible="1" />
                                                <field name="is_name_editable" invisible="1" />
                                                <field
                                                        name="name"
                                                        class="oe_inline"
                                                        readonly="True"
                                                />
                                        </h1>
                                        <group>
                                                <group>
                                                        <field
                                                                name="requested_by"
                                                                readonly="is_editable == False"
                                                        />

                                                        <field name="assigned_to"
                                                                readonly="is_editable == False"
                                                                invisible="1" />
                                                        <field name="pr_authorized_by" invisible="1" />
                                                        <field name="pr_approve_by" invisible="1" />
                                                        <field name="pr_approve_sign"
                                                                widget="signature" invisible="1"
                                                                options="{'full_name': 'display_name', 'size': ['',200]}" />


                                                        <field name="hod_approve_by" readonly="" />
                                                        <field name="hod_approve_sign"
                                                                widget="signature" invisible="1"
                                                                options="{'full_name': 'display_name', 'size': ['',200]}" />

                                                        <field name="hod_ho_id"
                                                                invisible="pr_type != 'head_office'" />
                                                        <field name="hod_ho_domain" invisible="1" />

                                                        <field name="finance_approve_by"
                                                                invisible="pr_type != 'head_office'"
                                                                readonly="state not in ('draft')" />
                                                        <field name="finance_ho_id"
                                                                invisible="pr_type != 'head_office'" />
                                                        <field name="finance_ho_domain"
                                                                invisible="1" />


                                                        <field name="local_manager_id"
                                                                invisible="pr_type != 'local'" />
                                                        <field name="local_manager_domain"
                                                                invisible="1" />

                                                        <field name="finance_user_id"
                                                                readonly="state not in ('draft')"
                                                                invisible="pr_type != 'local'"
                                                                required="pr_type == 'local'" />
                                                        <field name="local_finance_id"
                                                                invisible="pr_type != 'local'" />
                                                        <field name="local_finance_domain"
                                                                invisible="1" />

                                                        <field name="rc_user_id"
                                                                readonly="state not in ('draft')"
                                                                required="pr_type == 'local'"
                                                                invisible="pr_type != 'local'" />
                                                        <field name="local_rc_id"
                                                                invisible="pr_type != 'local'" />
                                                        <field name="local_rc_domain" invisible="1" />
                                                        <field
                                                                name="picking_type_id"
                                                                readonly="1" force_save="1"
                                                                invisible="1"
                                                                
                                                        />
                                                        <field
                                                                name="company_id"
                                                                groups="base.group_multi_company"
                                                                readonly="is_editable == False"
                                                        />

                                                </group>
                                                <group>
                                                        <field name="date_start"
                                                                readonly="is_editable == False" />
                                                        <field name="requisition_id" readonly="1"
                                                                string="Tender ID" />
                                                        <field
                                                                name="origin"
                                                                class="oe_inline"
                                                                readonly="is_editable == False"
                                                        />
                                                        <field name="description"
                                                                readonly="is_editable == False" />
                                                        <field name="group_id"
                                                                readonly="is_editable == False" />
                                                        <field name="pr_delivery_date" />
                                                        <field name="pr_closing_date" />
                                                        <field name="rejection_ntoe" readonly="1" />
                                                </group>
                                                <!-- <group>
                                                        <field
                                                                name="picking_type_id"
                                                                readonly="is_editable == False"
                                                        />
                                                        <field
                                                                name="company_id"
                                                                groups="base.group_multi_company"
                                                                readonly="is_editable == False"
                                                        />
                                                </group> -->
                                        </group>
                                        <notebook>
                                                <page string="Products">
                                                        <field name="line_ids"
                                                                readonly="pr_type == 'head_office' and state not in ('draft', 'hod_approve')">
                                                                <tree
                                                                        decoration-muted="cancelled == True"
                                                                        editable="bottom"
                                                                >
                                                                        <field name="product_id" />
                                                                        <field name="name" />
                                                                        <field name="product_qty" />
                                                                        <field
                                                                                name="product_uom_category_id"
                                                                                column_invisible="1"
                                                                        />
                                                                        <field
                                                                                name="product_uom_id"
                                                                                column_invisible="1"
                                                                                groups="!uom.group_uom"
                                                                        />
                                                                        <field
                                                                                name="product_uom_id"
                                                                                groups="uom.group_uom"
                                                                        />
                                                                        <field
                                                                                name="analytic_distribution"
                                                                                widget="analytic_distribution"
                                                                                groups="analytic.group_analytic_accounting"
                                                                                options="{'product_field': 'product_id', 'business_domain': 'purchase_order'}"
                                                                        />
                                                                        <field name="assigned_to" />
                                                                        <field name="date_required" />
                                                                        <field name="unit_price" />
                                                                        <field name="estimated_cost"
                                                                                widget="monetary" />
                                                                        <field name="currency_id"
                                                                                column_invisible="1" />
                                                                        <field
                                                                                name="company_id"
                                                                                groups="base.group_multi_company"
                                                                                widget="selection"
                                                                        />
                                                                        <field name="cancelled"
                                                                                column_invisible="1" />
                                                                        <field name="is_editable"
                                                                                column_invisible="1" />
                                                                        <field name="purchased_qty" />
                                                                        <field
                                                                                name="purchase_state"
                                                                                widget="badge"
                                                                                decoration-success="purchase_state == ('done')"
                                                                                decoration-muted="purchase_state == ('draft')"
                                                                                decoration-info="purchase_state in ('sent', 'purchase')"
                                                                                decoration-warning="purchase_state == ('to_approve')"
                                                                                decoration-danger="purchase_state == 'cancelled'"
                                                                        />
                                                                        <button
                                                                                name="action_show_details"
                                                                                type="object"
                                                                                icon="fa-list"
                                                                                title="Show Details"
                                                                                width="0.1"
                                                                                options='{"warn": true}'
                                                                        />
                                                                </tree>
                                                        </field>
                                                        <!--                            <group>-->
                                                        <group class="oe_subtotal_footer oe_right">
                                                                <field name="currency_id"
                                                                        column_invisible="1" />
                                                                <div
                                                                        class="oe_subtotal_footer_separator oe_inline">
                                                                        <label for="estimated_cost" />
                                                                </div>
                                                                <field
                                                                        name="estimated_cost"
                                                                        nolabel="1"
                                                                        class="oe_subtotal_footer_separator"
                                                                        widget="monetary"
                                                                        options="{'currency_field': 'currency_id'}"
                                                                />
                                                        </group>
                                                        <!--                            </group>-->
                                                </page>
                                        </notebook>
                                </sheet>
                                <div class="oe_chatter">
                                        <field name="message_follower_ids" widget="mail_followers" />
                                        <field name="activity_ids" widget="mail_activity" />
                                        <field name="message_ids" widget="mail_thread" />
                                </div>
                        </form>
                </field>
        </record>
        <record id="view_purchase_request_tree" model="ir.ui.view">
                <field name="name">purchase.request.tree</field>
                <field name="model">purchase.request</field>
                <field name="arch" type="xml">
                        <tree
                                decoration-bf="message_needaction==True"
                                decoration-info="state in ('draft','to_approve')"
                                decoration-muted="state in ('rejected')"
                                name="Purchase Request"
                        >
                                <field name="message_needaction" column_invisible="1" />
                                <field name="name" />
                                <field
                                        name="state"
                                        widget="badge"
                                        decoration-success="state in ('done', 'approved')"
                                        decoration-muted="state == 'draft'"
                                        decoration-warning="state == 'to_approve'"
                                        decoration-danger="state == 'rejected'"
                                        invisible="pr_type != 'head_office'"
                                        optional="show"
                                />
                                <field name="date_start" />
                                <field name="requested_by" widget="many2one_avatar_user" />
                                <field
                                        name="company_id"
                                        groups="base.group_multi_company"
                                        widget="selection"
                                />
                                <field name="activity_ids" widget="list_activity" optional="show" />
                                <field name="origin" />
                                <field name="currency_id" column_invisible="1" />
                                <field name="estimated_cost" optional="hide" />
                                <field name="pr_type" />
                                <field
                                        name="local_state"
                                        widget="badge"
                                        invisible="pr_type != 'local'"
                                        optional="hide"
                                />
                        </tree>
                </field>
        </record>
        <record id="view_purchase_request_search" model="ir.ui.view">
                <field name="name">purchase.request.list.select</field>
                <field name="model">purchase.request</field>
                <field name="arch" type="xml">
                        <search string="Search Purchase Request">
                                <field name="name" string="Purchase Request" />
                                <separator />
                                <field name="product_id" />
                                <field name="state" />
                                <filter
                                        name="unassigned"
                                        string="Unassigned"
                                        domain="[('assigned_to','=', False)]"
                                        help="Unassigned Request"
                                />
                                <separator />
                                <filter
                                        name="state_draft"
                                        string="Draft"
                                        domain="[('state','=','draft')]"
                                        help="Request is to be approved"
                                />
                                <filter
                                        name="state_to_approve"
                                        string="To Approve"
                                        domain="[('state','=','to_approve')]"
                                        help="Request is to be approved"
                                />
                                <filter
                                        name="state_approved"
                                        string="Approved"
                                        domain="[('state','=','approved')]"
                                        help="Request is approved"
                                />
                                <filter
                                        name="state_rejected"
                                        string="Rejected"
                                        domain="[('state','=','rejected')]"
                                        help="Request is rejected"
                                />
                                <filter
                                        name="state_done"
                                        string="Done"
                                        domain="[('state','=','done')]"
                                        help="Request is done"
                                />
                                <separator />
                                <filter
                                        string="Unread Messages"
                                        name="message_needaction"
                                        domain="[('message_needaction','=',True)]"
                                />
                                <filter
                                        name="assigned_to_me"
                                        domain="[('assigned_to','=', uid)]"
                                        help="Assigned to me"
                                />
                                <filter
                                        name="my_requests"
                                        domain="[('requested_by','=', uid)]"
                                        help="My requests"
                                />
                                <field name="requested_by" />
                                <field name="assigned_to" />
                                <field name="picking_type_id" />
                                <group expand="0" string="Group By...">
                                        <filter
                                                name="requested_by"
                                                string="Requested by"
                                                icon="fa-user"
                                                domain="[]"
                                                context="{'group_by':'requested_by'}"
                                        />
                                        <filter
                                                name="assigned_to"
                                                string="Assigned to"
                                                icon="fa-user"
                                                domain="[]"
                                                context="{'group_by':'assigned_to'}"
                                        />
                                        <filter
                                                name="source"
                                                string="Source"
                                                icon="fa-caret-square-o-left"
                                                domain="[]"
                                                context="{'group_by':'origin'}"
                                        />
                                        <filter
                                                name="start_date"
                                                string="Start Date"
                                                icon="fa-calendar"
                                                domain="[]"
                                                context="{'group_by':'date_start'}"
                                        />
                                </group>
                        </search>
                </field>
        </record>
        <record model="ir.actions.act_window" id="purchase_request_form_action">
                <field name="name">Purchase Requests (Head Office)</field>
                <field name="type">ir.actions.act_window</field>
                <field name="res_model">purchase.request</field>
                <field name="view_mode">tree,form</field>
                <field name="context">{"default_pr_type":'head_office'}</field>
                <field name="domain">[('pr_type', '=', 'head_office')]</field>
                <field name="search_view_id" ref="view_purchase_request_search" />
                <field name="help" type="html">
                        <p class="oe_view_nocontent_create">
                                Click to start a new purchase request process.
                        </p>
                        <p>
                                A purchase request is an instruction to Purchasing to procure
                                a certain quantity of materials services, so that they are
                                available at a certain point in time.
                        </p>
                </field>
        </record>
        <menuitem
                id="menu_purchase_request_pro_mgt"
                parent="purchase.menu_procurement_management"
                action="purchase_request_form_action"
        />
        <menuitem
                id="menu_purchase_request_act"
                name="Purchase Request for Head Office"
                sequence="10"
                parent="menu_purchase_request"
                action="purchase_request_form_action"
        />
        <menuitem
                id="parent_menu_purchase_request"
                name="Purchase Requests"
                sequence="22"
                web_icon="purchase_request,static/description/icon.png"
                groups="group_purchase_request_user,group_purchase_request_manager"
        />
        <menuitem
                id="menu_purchase_request"
                name="Purchase Requests"
                parent="parent_menu_purchase_request"
                groups="group_purchase_request_user,group_purchase_request_manager"
        />
        <menuitem
                id="menu_purchase_request_act"
                sequence="10"
                parent="menu_purchase_request"
                action="purchase_request_form_action"
        />
</odoo>