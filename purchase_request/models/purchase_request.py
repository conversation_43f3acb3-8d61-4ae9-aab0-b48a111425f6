# Copyright 2018-2019 <PERSON><PERSON><PERSON><PERSON>, S.L.
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0)

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import date
import logging
_logger = logging.getLogger(__name__)



_STATES = [
    ("draft", "Draft"),
    ("hod_approve", "HOD Approval"),
    ("coo_approve", "COO Approval"),
    ("to_approve", "Finance Clearance"),
    ("cfo_approve", "CFO Approval"),
    ("approved", "Approved"),
    ("rejected", "Rejected"),
    ("done", "Done"),
]


class PurchaseRequest(models.Model):
    _name = "purchase.request"
    _description = "Purchase Request"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _order = "id desc"

    # @api.model
    # def _company_get(self):
    #     return self.env["res.company"].browse(self.env.company.id)

    @api.model
    def _get_default_requested_by(self):
        return self.env["res.users"].browse(self.env.uid)

    @api.model
    def _get_default_name(self):
        return self.env["ir.sequence"].next_by_code("purchase.request")

    @api.onchange('company_id')
    def get_picking_type(self):
        if self.company_id:
            type_obj = self.env["stock.picking.type"]
            company_id = self.company_id.id
            type = type_obj.search(
                [("code", "=", "incoming"), ("warehouse_id.company_id", "=", company_id)],
                limit=1
            )
            self.picking_type_id = type.id
        else:
            type_obj = self.env["stock.picking.type"]
            type = type_obj.search(
                    [("code", "=", "incoming"), ("warehouse_id", "=", False)], limit=1
                )
            self.picking_type_id = type.id

    @api.model
    def _default_picking_type(self):
        type_obj = self.env["stock.picking.type"]
        company_id = self.env.context.get("company_id") or self.env.company.id
        # company_id = self.company_id.id
        types = type_obj.search(
            [("code", "=", "incoming"), ("warehouse_id.company_id", "=", company_id)]
        )
        if not types:
            types = type_obj.search(
                [("code", "=", "incoming"), ("warehouse_id", "=", False)]
            )
        return types[:1]

    @api.depends("state")
    def _compute_is_editable(self):
        for rec in self:
            if rec.state in ("approved", "rejected", "done"):
                rec.is_editable = False
            else:
                rec.is_editable = True

    name = fields.Char(
        string="Request Reference",
        required=True,
        default=lambda self: _("New"),
        tracking=True,
    )
    is_name_editable = fields.Boolean(
        default=lambda self: self.env.user.has_group("base.group_no_one"),
    )
    origin = fields.Char(string="Source Document")
    date_start = fields.Date(
        string="Creation date",
        help="Date when the user initiated the request.",
        default=fields.Date.context_today,
        tracking=True,
    )
    requested_by = fields.Many2one(
        comodel_name="res.users",
        required=True,
        copy=False,
        tracking=True,
        default=_get_default_requested_by,
        index=True,
    )
    assigned_to = fields.Many2one(
        comodel_name="res.users",
        string="Approver",
        tracking=True,
        domain=lambda self: [
            (
                "groups_id",
                "in",
                self.env.ref("purchase_request.group_purchase_request_manager").id,
            )
        ],
        index=True,
    )
    description = fields.Text()
    company_id = fields.Many2one(
        comodel_name="res.company",
        string="Project",
        required=False,
        # default=_company_get,
        tracking=True,
        domain="[('name', '!=', 'Friendship NGO')]"
    )
    line_ids = fields.One2many(
        comodel_name="purchase.request.line",
        inverse_name="request_id",
        string="Products to Purchase",
        readonly=False,
        copy=True,
        tracking=True,
    )
    product_id = fields.Many2one(
        comodel_name="product.product",
        related="line_ids.product_id",
        string="Product",
        readonly=True,
    )
    state = fields.Selection(
        selection=_STATES,
        string="Status",
        index=True,
        tracking=True,
        required=True,
        copy=False,
        default="draft",
    )
    is_editable = fields.Boolean(compute="_compute_is_editable", readonly=True)
    to_approve_allowed = fields.Boolean(compute="_compute_to_approve_allowed")
    picking_type_id = fields.Many2one(
        comodel_name="stock.picking.type",
        string="Picking Type",
        required=False,
    )
    group_id = fields.Many2one(
        comodel_name="procurement.group",
        string="Procurement Group",
        copy=False,
        index=True,
    )
    line_count = fields.Integer(
        string="Purchase Request Line count",
        compute="_compute_line_count",
        readonly=True,
    )
    move_count = fields.Integer(
        string="Stock Move count", compute="_compute_move_count", readonly=True
    )
    purchase_count = fields.Integer(
        string="Purchases count", compute="_compute_purchase_count", readonly=True
    )
    currency_id = fields.Many2one(related="company_id.currency_id", readonly=True)
    estimated_cost = fields.Monetary(
        compute="_compute_estimated_cost",
        string="Total Estimated Cost",
        store=True,
    )
    
    
    @api.onchange('pr_delivery_date')
    def _onchange_pr_delivery_date(self):
        if self.pr_delivery_date and self.pr_delivery_date < date.today():
            return {
                'warning': {
                    'title': _('Invalid Date'),
                    'message': _('Delivery deadline cannot be set to a past date. Please select today\'s date or a future date.')
                },
                'value': {'pr_delivery_date': False}
            }
    
    @api.constrains('pr_delivery_date')
    def _check_delivery_date(self):
        for record in self:
            if record.pr_delivery_date and record.pr_delivery_date < date.today():
                raise ValidationError(_("Delivery deadline cannot be set to a past date."))

    @api.depends("line_ids", "line_ids.estimated_cost")
    def _compute_estimated_cost(self):
        for rec in self:
            rec.estimated_cost = sum(rec.line_ids.mapped("estimated_cost"))

    @api.depends("line_ids")
    def _compute_purchase_count(self):
        for rec in self:
            rec.purchase_count = len(rec.mapped("line_ids.purchase_lines.order_id"))

    def action_view_purchase_order(self):
        action = self.env["ir.actions.actions"]._for_xml_id("purchase.purchase_rfq")
        lines = self.mapped("line_ids.purchase_lines.order_id")
        if len(lines) > 1:
            action["domain"] = [("id", "in", lines.ids)]
        elif lines:
            action["views"] = [
                (self.env.ref("purchase.purchase_order_form").id, "form")
            ]
            action["res_id"] = lines.id
        return action

    @api.depends("line_ids")
    def _compute_move_count(self):
        for rec in self:
            rec.move_count = len(
                rec.mapped("line_ids.purchase_request_allocation_ids.stock_move_id")
            )

    def action_view_stock_picking(self):
        action = self.env["ir.actions.actions"]._for_xml_id(
            "stock.action_picking_tree_all"
        )
        # remove default filters
        action["context"] = {}
        lines = self.mapped(
            "line_ids.purchase_request_allocation_ids.stock_move_id.picking_id"
        )
        if len(lines) > 1:
            action["domain"] = [("id", "in", lines.ids)]
        elif lines:
            action["views"] = [(self.env.ref("stock.view_picking_form").id, "form")]
            action["res_id"] = lines.id
        return action

    @api.depends("line_ids")
    def _compute_line_count(self):
        for rec in self:
            rec.line_count = len(rec.mapped("line_ids"))

    def action_view_purchase_request_line(self):
        action = (
            self.env.ref("purchase_request.purchase_request_line_form_action")
            .sudo()
            .read()[0]
        )
        lines = self.mapped("line_ids")
        if len(lines) > 1:
            action["domain"] = [("id", "in", lines.ids)]
        elif lines:
            action["views"] = [
                (self.env.ref("purchase_request.purchase_request_line_form").id, "form")
            ]
            action["res_id"] = lines.ids[0]
        return action

    @api.depends("state", "line_ids.product_qty", "line_ids.cancelled")
    def _compute_to_approve_allowed(self):
        for rec in self:
            rec.to_approve_allowed = rec.state == "draft" and any(
                not line.cancelled and line.product_qty for line in rec.line_ids
            )

    def copy(self, default=None):
        default = dict(default or {})
        self.ensure_one()
        default.update({"state": "draft", "name": "New"})
        return super().copy(default)

    @api.model
    def _get_partner_id(self, request):
        user_id = request.assigned_to or self.env.user
        return user_id.partner_id.id

    @api.model_create_multi
    def create(self, vals_list):
        # for vals in vals_list:
        #     if vals.get("name", _("New")) == _("New"):
        #         vals["name"] = self._get_default_name()
        requests = super().create(vals_list)
        for vals, request in zip(vals_list, requests, strict=True):
            if vals.get("assigned_to"):
                partner_id = self._get_partner_id(request)
                request.message_subscribe(partner_ids=[partner_id])
        return requests

    def write(self, vals):
        res = super().write(vals)
        for request in self:
            if vals.get("assigned_to"):
                partner_id = self._get_partner_id(request)
                request.message_subscribe(partner_ids=[partner_id])
        return res

    def _can_be_deleted(self):
        self.ensure_one()
        return self.state == "draft"

    def unlink(self):
        for request in self:
            if not request._can_be_deleted():
                raise UserError(
                    _("You cannot delete a purchase request which is not draft.")
                )
        return super().unlink()

    def button_draft(self):
        self.mapped("line_ids").do_uncancel()
        return self.write({"state": "draft"})

    def button_to_approve(self):
        self.to_approve_allowed_check()
        return self.write({"state": "to_approve"})

    def button_approved(self):
        self.pr_approve_by = self.env.user.id
        self.pr_approve_sign = self.env.user.sign_signature
        return self.write({"state": "approved"})

    def button_rejected(self):
        return {
            "type": "ir.actions.act_window",
            "name": "Rejection Note",
            "res_model": "rejection.note.wizard",
            "view_mode": "form",
            "target": "new",
            "context": {
                "default_requisition_id": self.id,
            },
        }
        # self.mapped("line_ids").do_cancel()
        # return self.write({"state": "rejected"})

    def button_done(self):
        return self.write({"state": "done"})

    def check_auto_reject(self):
        """When all lines are cancelled the purchase request should be
        auto-rejected."""
        for pr in self:
            if not pr.line_ids.filtered(lambda line: line.cancelled is False):
                pr.write({"state": "rejected"})

    def to_approve_allowed_check(self):
        for rec in self:
            if not rec.to_approve_allowed:
                raise UserError(
                    _(
                        "You can't request an approval for a purchase request "
                        "which is empty. (%s)"
                    )
                    % rec.name
                )

    requisition_id = fields.Many2one("purchase.requisition", string="Requisition ID")

    def action_create_tender(self):
        for rec in self:
            line_list = []
            for item in rec.line_ids:
                line_values = {
                    "product_id": item.product_id.id,
                    "product_description_variants": item.name,
                    "product_qty": item.product_qty,
                    "qty_ordered": 0.00,
                    "product_uom_id": item.product_uom_id.id,
                    "analytic_distribution": item.analytic_distribution,
                    "price_unit": item.estimated_cost,
                }
                line_list.append((0, 0, line_values))
            # print(line_list)
            tender = rec.env["purchase.requisition"].create(
                {
                    "user_id": rec.requested_by.id,
                    "requisition_type": "purchase_template",
                    "purchase_request": rec.id,
                    "currency_id": rec.currency_id.id,
                    "line_ids": line_list,
                }
            )
            rec.requisition_id = tender.id
            action = {
                "name": _("Purchase Agreement"),
                "type": "ir.actions.act_window",
                "res_model": "purchase.requisition",
                "target": "current",
            }

            order = tender.id
            action["res_id"] = order
            action["view_mode"] = "form"
            action["views"] = [
                (
                    self.env.ref(
                        "purchase_requisition.view_purchase_requisition_form"
                    ).id,
                    "form",
                )
            ]

            return action

    def finance_approve(self):
        for rec in self:
            rec.finance_approve_by = rec.env.user.id
            rec.write({"state": "cfo_approve"})

    # def send_to_finance(self):
    #     for rec in self:
    #         rec.write({"state": "to_approve"})

    # def hod_approve(self):
    #     for rec in self:
    #         if rec.rel_emp.department_id.need_coo_aproval:
    #             if rec.hod_approve_by:
    #                 if rec.hod_approve_by == rec.env.user:
    #                     rec.write({"state": "coo_approve"})
    #                 else:
    #                     raise UserError(_('You are not allowed to approve this'))
    #             else:
    #                 raise UserError(_('Requester not select the HOD approver, please reject and reset then inform the requester to select HOD approver.'))
    #         else:

    #             rec.write({"state": "cfo_approve"})

    def coo_approve(self):
        for rec in self:
            rec.coo_approve_by = rec.env.user.id
            rec.write({"state": "to_approve"})

    def send_to_hod(self):
        for rec in self:
            if rec.hod_approve_by:
                if not "PR-" in rec.name:
                    rec.name = rec._get_default_name()
                rec.write({"state": "hod_approve"})
            else:
                raise ValidationError(f"Requester not select the HOD/Manager User, inform the requester to select HOD/Manager User.")

    def update_budget_pr_cost(self):
        for rec in self:
            if rec.budget_line_id:
                pr_cost = rec.estimated_cost
                rec.budget_line_id.pr_stage += pr_cost

    pr_approve_by = fields.Many2one("res.users", string="Approved By")
    pr_approve_sign = fields.Binary(string="Sign")


    @api.depends("requested_by")
    def _compute_rel_emp_domain(self):
        for record in self:
            # if not record.rel_emp:
            common_user_employees = self.env["hr.employee"].sudo().search(
                [("common_user_id", "=", record.requested_by.id)]
            )
            if common_user_employees:
                # record.rel_emp = False
                record.rel_emp_domain = [
                    ("common_user_id", "=", record.requested_by.id)
                ]
            else:
                same_user_employee = self.env["hr.employee"].sudo().search(
                        [("user_id", "=", record.requested_by.id)]
                    )
                    
                record.rel_emp_domain = [("user_id", "=", record.requested_by.id)]
    
    @api.onchange('requested_by')
    def _get_employee_data(self):
        _logger.info("Requested by: %s", self.requested_by.name)
        same_user_employee = self.env["hr.employee"].sudo().search(
                        [("user_id", "=", self.requested_by.id)]
                    )
                
        if len(same_user_employee) == 1:
            self.rel_emp = same_user_employee.id
        else:
            self.rel_emp = False

    rel_emp = fields.Many2one(
        comodel_name="hr.employee",
        string="Requesting Employee *",
        store=True,
        domain="rel_emp_domain"
    )

    rel_emp_domain = fields.Binary(compute="_compute_rel_emp_domain", store=False)
    
    hod_ho_id = fields.Many2one("hr.employee", "HOD for HO PR", domain="hod_ho_domain")
    hod_ho_domain = fields.Binary(compute="_compute_hod_ho_domain", store=False)

    finance_ho_id = fields.Many2one("hr.employee", "Finance for HO PR", store=True, domain="finance_ho_domain")
    finance_ho_domain = fields.Binary(compute="_compute_finance_ho_domain", store=False)
    finance_approve_by = fields.Many2one("res.users", string="Finance User", store=True,domain=lambda self: [
            ('groups_id', 'in', self.env.ref('purchase_request.purchase_request_finanace_user').id)
        ])
    ffinance_approve_sign = fields.Binary(
        related="finance_approve_by.sign_signature", string="Finance Sign"
    )

    # @api.depends("rel_emp", "rel_emp.department_id", "pr_type")
    # def _compute_ho_finance_user_id(self):
    #     for rec in self:
    #         if rec.pr_type == 'head_office':
    #             if (rec.rel_emp and rec.rel_emp.department_id and rec.rel_emp.department_id.ho_finance_id and rec.rel_emp.department_id.ho_finance_id.user_id):
    #                 rec.finance_approve_by = rec.rel_emp.department_id.ho_finance_id.user_id
    #         else:
    #             rec.finance_approve_by = False
    
    @api.depends("rel_emp", "rel_emp.department_id")
    def _compute_hod_ho_domain(self):
        for record in self:
            if record.rel_emp and record.rel_emp.department_id and record.rel_emp.department_id.hod_id and record.rel_emp.department_id.hod_id.related_hod_ids:
                record.hod_ho_domain = [("id", "in", record.rel_emp.department_id.hod_id.related_hod_ids.ids)]
            else:
                record.hod_ho_domain = []

    @api.depends("finance_approve_by", "finance_approve_by.employee_id")
    def _compute_finance_ho_domain(self):
        for record in self:
            employee = record.env['hr.employee'].sudo().search([('user_id', '=', record.finance_approve_by.id)], limit=1)
            # if record.finance_approve_by.employee_id and record.finance_approve_by.employee_id.related_ho_finance_ids:
            #     record.finance_ho_domain = [("id", "in", record.finance_approve_by.employee_id.related_ho_finance_ids.ids)]
            if employee:
                record.finance_ho_domain = [("id", "in", employee.related_ho_finance_ids.ids)]
            else:
                record.finance_ho_domain = [("id", "=", 0)]
    
    local_manager_id = fields.Many2one("hr.employee", "Manager for Local PR", domain="local_manager_domain")
    local_manager_domain = fields.Binary(compute="_compute_local_manager_domain", store=False)
    
    @api.depends("rel_emp", "rel_emp.department_id")
    def _compute_local_manager_domain(self):
        for record in self:
            if record.rel_emp and record.rel_emp.department_id and record.rel_emp.department_id.local_manager_id and record.rel_emp.department_id.local_manager_id.related_hod_ids:
                record.local_manager_domain = [("id", "in", record.rel_emp.department_id.local_manager_id.related_hod_ids.ids)]
            else:
                record.local_manager_domain = []
    
   
    # finance_ho_domain = fields.Binary(compute="_compute_finance_ho_domain", store=False)
    
    # @api.depends("rel_emp", "rel_emp.department_id", "rel_emp.department_id.local_rc_id")
    # def _compute_rc_user_id(self):
    #     for record in self:
    #         if record.rel_emp and record.rel_emp.department_id and record.rel_emp.department_id.local_rc_id and record.rel_emp.department_id.local_rc_id.user_id:
    #             record.rc_user_id = record.rel_emp.department_id.local_rc_id.user_id
    #         else:
    #             record.rc_user_id = False
    rc_user_id = fields.Many2one(
        "res.users",
        string="RC User",
        compute="_compute_rc_user_id",
        store=True,
    )
    finance_user_id = fields.Many2one(
        "res.users",
        string="Finance User",
        compute="_compute_finance_user_id",
        store=True,
    )
    
    local_rc_id = fields.Many2one("hr.employee", "RC for Local PR", domain="local_rc_domain", store=True)
    local_rc_domain = fields.Binary(compute="_compute_local_rc_domain", store=False)
    local_finance_id = fields.Many2one("hr.employee", "Finance for Local PR" , domain = "local_finance_domain", store=True)
    local_finance_domain = fields.Binary(compute="_compute_local_finance_domain", store=False)
    

    @api.depends("rel_emp", "rel_emp.department_id", "pr_type")
    def _compute_rc_user_id(self):
        for rec in self:
            if rec.pr_type == 'local':
                if (rec.rel_emp and rec.rel_emp.department_id and rec.rel_emp.department_id.local_rc_id and rec.rel_emp.department_id.local_rc_id.user_id):
                    rec.rc_user_id = rec.rel_emp.department_id.local_rc_id.user_id
            else:
                rec.rc_user_id = False
    
    @api.depends("rel_emp", "rel_emp.department_id", "pr_type")
    def _compute_finance_user_id(self):
        for rec in self:
            if rec.pr_type == 'local':
                if (rec.rel_emp and rec.rel_emp.department_id and rec.rel_emp.department_id.local_finance_id and rec.rel_emp.department_id.local_finance_id.user_id):
                    rec.finance_user_id = rec.rel_emp.department_id.local_finance_id.user_id
            else:
                rec.finance_user_id = False
    
    @api.depends("rel_emp", "rel_emp.department_id")
    def _compute_local_rc_domain(self):
        for record in self:
            if record.rel_emp and record.rel_emp.department_id and record.rel_emp.department_id.local_rc_id and record.rel_emp.department_id.local_rc_id.related_hod_ids:
                record.local_rc_domain = [("id", "in", record.rel_emp.department_id.local_rc_id.related_hod_ids.ids)]
            else:
                record.local_rc_domain = []
    
    
    @api.depends("rel_emp", "rel_emp.department_id")
    def _compute_local_finance_domain(self):
        for record in self:
            if record.rel_emp and record.rel_emp.department_id and record.rel_emp.department_id.local_finance_id and record.rel_emp.department_id.local_finance_id.related_finance_ids:
                record.local_finance_domain = [("id", "in", record.rel_emp.department_id.local_finance_id.related_finance_ids.ids)]
            else:
                record.local_finance_domain = []

    
    

    hod_approve_by = fields.Many2one(
        "res.users",
        string="HOD/Manager User",
        required=False,
        compute="get_hod_data",
        store=True,
    )
    hod_approve_sign = fields.Binary(
        related="hod_approve_by.sign_signature", string="Hod Sign"
    )

    @api.depends("rel_emp", "rel_emp.department_id", "pr_type")
    def get_hod_data(self):
        for rec in self:
            if rec.pr_type == 'head_office':
                if (rec.rel_emp and rec.rel_emp.department_id and rec.rel_emp.department_id.hod_id and rec.rel_emp.department_id.hod_id.user_id):
                    rec.hod_approve_by = rec.rel_emp.department_id.hod_id.user_id
            else:
                if (rec.rel_emp and rec.rel_emp.department_id and rec.rel_emp.department_id.local_manager_id and rec.rel_emp.department_id.local_manager_id.user_id):
                    rec.hod_approve_by = rec.rel_emp.department_id.local_manager_id.user_id

    def hod_approve_button(self):
        for rec in self:
            if rec.hod_approve_by:
                # if rec.budget_line_id:
                rec.update_budget_pr_cost()
                if rec.hod_approve_by == rec.env.user or rec.env.user.has_group("purchase_request.purchase_request_hod_user"):
                    if rec.rel_emp.department_id.need_coo_aproval and rec.rel_emp.department_id.estimated_amount < rec.estimated_cost:
                        if rec.hod_approve_by != rec.env.user:
                            rec.hod_approve_by = rec.env.user.id 
                        rec.write({"state": "coo_approve"})
                    else:
                        if rec.hod_approve_by != rec.env.user:
                            rec.hod_approve_by = rec.env.user.id 
                        rec.write({"state": "to_approve"})
                else:
                    raise UserError(_("You are not allowed to approve this"))
            else:
                raise UserError(
                    _(
                        "Requester not select the HOD approver, please reject and reset then inform the requester to select HOD approver."
                    )
                )
            # if rec.rel_emp.department_id.need_coo_aproval and rec.rel_emp.department_id.estimated_amount < rec.estimated_cost:
            #     if rec.hod_approve_by:
            #         if rec.hod_approve_by == rec.env.user:
            #             rec.write({"state": "coo_approve"})
            #         else:
            #             raise UserError(_("You are not allowed to approve this"))
            #     else:
            #         raise UserError(
            #             _(
            #                 "Requester not select the HOD approver, please reject and reset then inform the requester to select HOD approver."
            #             )
            #         )
            # else:
            #     rec.write({"state": "to_approve"})

    coo_approve_by = fields.Many2one("res.users", string="COO Approved By")
    coo_approve_sign = fields.Binary(
        related="coo_approve_by.sign_signature", string="Coo Sign"
    )

    pr_authorized_by = fields.Many2one("res.users", string="Authorized by:")
    pr_delivery_date = fields.Date("Delivery Deadline", required=True)
    pr_closing_date = fields.Date("Closing Date")

    rejection_ntoe = fields.Text("Rejection Note", copy=False)
    pr_type = fields.Selection([('head_office', 'Head Office'), ('local', 'Local')], string='PR Type', store=True, required="1")
    local_state = fields.Selection([
        ("draft", "Draft"),
        ("manager_approve", "Manager Approval"),
        ("finance_approve", "Finance Approval"),
        ('rc_approve', 'RC Approval'),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("done", "Done"),
    ], string='Local PR Status', default='draft')

    # rel_emp = fields.Many2one(
    #     comodel_name='hr.employee',
    #     string="Related Employee",
    #     domain="[('common_user_id', '=', requested_by)]"
    # )
    # pr_project_name = fields.Many2one('project.project', string="Project")
    # pr_admin_note = fields.Text(string="Admin Note")

    # def cfo_approve(self):
    #     for rec in self:
    #         rec.write({"state": "approved"})

    @api.onchange("company_id")
    def _onchange_company_id(self):
        if self.company_id:
            type_obj = self.env["stock.picking.type"]
            company_id = self.company_id.id
            type = type_obj.search(
                [("code", "=", "incoming"), ("warehouse_id.company_id", "=", company_id)],
                limit=1
            )
            self.picking_type_id = type.id
        else:
            type_obj = self.env["stock.picking.type"]
            type = type_obj.search(
                    [("code", "=", "incoming"), ("warehouse_id", "=", False)], limit=1
                )
            self.picking_type_id = type.id