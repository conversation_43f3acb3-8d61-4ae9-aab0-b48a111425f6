from odoo import models, api

class ResUsers(models.Model):
    _inherit = "res.users"

    def _auto_select_companies(self):
        """Force-select all allowed companies for user"""
        main_company = self.env['res.company'].sudo().search([('id', '=', 1)], limit=1)
        all_companies = self.env['res.company'].search([])
        if all_companies:
            # Update user's company assignments
            self.sudo().write({
                'company_ids': [(6, 0, all_companies.ids)],
                'company_id': main_company.id
            })
            # Update environment context
            self = self.with_context(
                allowed_company_ids=all_companies.ids,
                company_id=main_company.id
            )
            return True
        return False
    
    @api.model
    def _update_last_login(self):
        """Hook to auto-select companies on login"""
        res = super()._update_last_login()
        self._auto_select_companies()
        return res