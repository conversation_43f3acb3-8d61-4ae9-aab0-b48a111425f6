from odoo.addons.web.controllers.home import Home as WebHome
from odoo.addons.web.controllers.utils import is_user_internal
from odoo.http import request


import logging
_logger = logging.getLogger(__name__)



class Home(WebHome):

    def _login_redirect(self, uid, redirect=None):
        # user = request.env['res.users'].sudo().browse(uid)
        # # if not redirect and len(user.company_ids) > 1:
        # if not redirect and user.company_ids:
        #     company_ids = user.company_ids.ids
            
        #     if 1 in company_ids:
                
        #         company_ids.remove(1)
                
        #         company_ids.insert(0, 1)

        #     cids = '-'.join(str(cid) for cid in company_ids)
        #     redirect = f'/web#action=menu&cids={cids}'
        res = super()._login_redirect(uid, redirect=redirect)
        _logger.info(f"Redirecting user {res}")
        _logger.info(f"Redirecting URLS------------>>> {redirect}")
        return res