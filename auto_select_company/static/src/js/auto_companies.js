/** @odoo-module **/

import { registry } from "@web/core/registry";
import { patch } from "@web/core/utils/patch";
import { session } from "@web/session";
import { userService } from "@web/core/user_service";

// Method 1: Patch the user service (recommended)
const userServicePatched = {
    dependencies: [...userService.dependencies, "router"],
    
    async start(env, deps) {
        const user = await userService.start(env, deps);
        
        
        if (session.user_companies?.allowed_companies) {
            const allowedCompanyIds = Object.keys(session.user_companies.allowed_companies);
            // const allowedCompanyIds = Object.keys(session.user_companies.allowed_companies);
            const currentHash = new URLSearchParams(window.location.hash.slice(1));
            // currentHash.set('cids', allowedCompanyIds.join(','));
            // window.location.hash = currentHash.toString();
            if (!currentHash.has('cids')) {
                currentHash.set('cids', allowedCompanyIds.join(','));
                window.location.hash = currentHash.toString();
            }
            // Only update if not already set
            // if (!env.services.router.current.hash.cids) {
            // console.log("userServicePatched", session.user_companies);
            // env.services.router.navigate({
            //     ...env.services.router.current,
            //     hash: {
            //         ...env.services.router.current.hash,
            //         cids: allowedCompanyIds.join('-'),
            //     },
            // }, { replace: true });
            // }
        }
        return user;
    }
};
registry.category("services").add("user", userServicePatched, { force: true });



///////////////////////////////////////////////////////////////////////

// /** @odoo-module **/

// import { registry } from "@web/core/registry";
// import { patch } from "@web/core/utils/patch";
// import { session } from "@web/session";

// // Correct patch syntax for Odoo 17
// const sessionPatch = {
//     async login(login, password) {
//         const result = await super.login(login, password);
//         if (result && result.uid) {
//             // Force all companies in URL after successful login
//             const companies = this.user_companies?.allowed_companies;
//             if (companies && Object.keys(companies).length > 1) {
//                 const cids = Object.keys(companies).join('-');
//                 window.location.hash = `action=menu&cids=${cids}`;
//             }
//         }
//         return result;
//     },
// };

// patch(session, sessionPatch);

// // Service to handle initial page load
// const autoCompaniesService = {
//     dependencies: ["user", "company"],
    
//     async start(env, { user, company }) {
//         // Check URL for existing cids parameter
//         const hashParams = new URLSearchParams(window.location.hash.slice(1));
//         const cids = hashParams.get('cids');
        
//         if (!cids && user.allowedCompanyIds?.length > 0) {
//             // Redirect with all company IDs if not already in URL
//             const allCids = user.allowedCompanyIds.join('-');
//             window.location.hash = `action=menu&cids=${allCids}`;
//             window.location.reload();
//         }
//     },
// };

// registry.category("services").add("auto_companies", autoCompaniesService);