from odoo import _, api, fields, models
from odoo.exceptions import UserError
from datetime import date, timedelta
import logging
_logger = logging.getLogger(__name__)



class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    
    def action_create_invoice(self):
        """
        Override the action_create_invoice method to set the account_id of the invoice
        to the GL code from the purchase request.
        """
        res = super(PurchaseOrder, self).action_create_invoice()
        
        # Get the invoice(s) created by the super method
        if isinstance(res, dict) and res.get('res_id'):
            invoice_ids = [res['res_id']]
        elif isinstance(res, dict) and res.get('domain'):
            domain = res.get('domain', [])
            invoice_ids = self.env['account.move'].search(domain).ids
        else:
            invoice_ids = self.env['account.move'].search([
                ('invoice_origin', 'in', self.mapped('name'))
            ]).ids
        
        invoices = self.env['account.move'].browse(invoice_ids)
        
        for invoice in invoices:
            # Create a mapping of purchase lines to invoice lines
            po_line_to_inv_line = {}
            for inv_line in invoice.invoice_line_ids:
                # Skip if this is a section or note
                if inv_line.display_type in ('line_section', 'line_note'):
                    continue
                    
                purchase_line = inv_line.purchase_line_id
                if purchase_line:
                    po_line_to_inv_line[purchase_line.id] = inv_line
            
            _logger.info(f"PO line to invoice line mapping: {po_line_to_inv_line}")
            # Update accounts based on PR budget
            for order in self:
                _logger.info(f"Processing order:--------------------- {order.name}")
                for line in order.order_line:
                    _logger.info(f"Processing line:--------------------- {line}")
                    if (line.id in po_line_to_inv_line and 
                        line.pr_request_line and 
                        line.pr_request_line.project_budget_id and 
                        line.pr_request_line.project_budget_id.gl_code_and_name):
                        
                        gl_account = line.pr_request_line.project_budget_id.gl_code_and_name
                        inv_line = po_line_to_inv_line[line.id]
                        
                        
                        # Check if gl_code_and_name is already an account.account record
                        if gl_account and hasattr(gl_account, '_name') and gl_account._name == 'account.account':
                            inv_line.account_id = gl_account.id
                            _logger.info(f"Updated invoice line account to: {gl_account.code} - {gl_account.name}")
                        else:
                            _logger.warning(f"gl_code_and_name is not a valid account")
        return res
    