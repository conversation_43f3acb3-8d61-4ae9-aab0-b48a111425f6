from odoo import _, api, fields, models
from odoo.exceptions import UserError
from datetime import date, timedelta
import logging
_logger = logging.getLogger(__name__)



class PurchaseRequest(models.Model):
    _inherit = 'purchase.request'
    
    budget_line_id = fields.Many2one(
        'project.budget',
        string='Budget Line',
        help="The budget line associated with this purchase request.",
        domain="budget_domain"
    )
    
    @api.depends('company_id')
    def _compute_budget_domain(self):
        for record in self:
            if record.company_id:
                record.budget_domain = f"[('company_id', '=', {record.company_id.id})]"
            else:
                record.budget_domain = "[('id', '=', False)]"

    budget_domain = fields.Char(compute='_compute_budget_domain')
    
    assigned_to_emp = fields.Many2one(
        'hr.employee',
        string='Assigned To',
        help="Employee responsible for this purchase request."
    )
    
    
    @api.onchange('assigned_to_emp')
    def _onchange_assigned_to_emp(self):
        if self.assigned_to_emp:
            for line in self.line_ids:
                line.assigned_to_emp = self.assigned_to_emp

    @api.model
    def create(self, vals):
        if vals.get('assigned_to_emp') and vals.get('line_ids'):
            new_lines = []
            for line in vals.get('line_ids'):
                _logger.info(f"Line[0]:------------------ {line[0]}")
                _logger.info(f"len(line):------------------ {len(line)}")
                # For create commands (0, virtual_id, values_dict)
                if line[0] == 0 and len(line) >= 3:
                    # Modify the values dictionary (third element in the list)
                    line[2]['assigned_to_emp'] = vals['assigned_to_emp']
                new_lines.append(line)
            vals['line_ids'] = new_lines
        return super(PurchaseRequest, self).create(vals)
    
    
    
    
    @api.onchange('budget_line_id')
    def _onchange_budget_line_id(self):
        if self.budget_line_id:
            for line in self.line_ids:
                line.project_budget_id = self.budget_line_id

    @api.model
    def create(self, vals):
        if vals.get('budget_line_id') and vals.get('line_ids'):
            new_lines = []
            for line in vals.get('line_ids'):
                # For create commands (0, virtual_id, values_dict)
                if line[0] == 0 and len(line) >= 3:
                    # Modify the values dictionary (third element in the list)
                    line[2]['project_budget_id'] = vals['budget_line_id']
                new_lines.append(line)
            vals['line_ids'] = new_lines
        return super(PurchaseRequest, self).create(vals)