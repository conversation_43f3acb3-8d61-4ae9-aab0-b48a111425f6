from odoo import models, fields, api

class ResDonor(models.Model):
    _inherit = 'res.partner'

    _sql_constraints = [('donor_name_unique', 'UNIQUE(name)', 'Donor name must be unique!')]

    @api.model
    def get_active_donors(self):
        """
        Function to get all active donors
        Returns: List of dictionaries containing id and name of active donors
        """
        donors = self.env['res.partner'].sudo().search([('is_donor', '=', True)])
        return [{'id': donor.id, 'name': donor.name, 'donor_type': donor.company_type} for donor in donors]

    @api.model
    def get_donor(self, donor_id):
        """
        Function to get a single donor by ID
        Args:
            donor_id: ID of the donor to retrieve
        Returns: Dictionary containing donor details or False if not found
        """
        donor = self.env['res.partner'].sudo().browse(donor_id)
        if not donor.exists():
            return False
        return {
            'id': donor.id,
            'name': donor.name,
            'active': donor.active,
            'email': donor.email,
            'phone': donor.phone,
            'mobile': donor.mobile,
            'is_donor': donor.is_donor if hasattr(donor, 'is_donor') else False,
            'ref': donor.ref,
            'website': donor.website,
            'address': ', '.join(filter(None, [donor.street, donor.street2, donor.city, donor.zip, donor.country_id.name if donor.country_id else None])),
            'relation_began': donor.relation_began if hasattr(donor, 'relation_began') else False,
            'focal_person': donor.focal_person if hasattr(donor, 'focal_person') else False,
            'focal_person_email': donor.focal_person_email if hasattr(donor, 'focal_person_email') else False,
            'remarks': donor.remarks if hasattr(donor, 'remarks') else False,
            'company_id': donor.company_id.id if donor.company_id else False,
            'country_id': [donor.country_id.id, donor.country_id.name] if donor.country_id else False,
            'parent_id': [donor.parent_id.id, donor.parent_id.name] if donor.parent_id else False,
            'currency_id': [donor.currency_id.id, donor.currency_id.name] if donor.currency_id else False,
            'company_type': donor.company_type,
            'category_id': [cat.id for cat in donor.category_id],
            'create_date': donor.create_date,
            'write_date': donor.write_date,
            'supplier_rank': donor.supplier_rank,
            'customer_rank': donor.customer_rank,
            'commercial_company_name': donor.commercial_company_name,
            'complete_name': donor.complete_name
        }

    @api.model
    def create_donor(self, vals):
        """
        Function to create a donor
        Returns: The json object of the created donor
        """
        donor = super(ResDonor, self).create(vals)
        return {
            'id': donor.id,
            'name': donor.name,
            'active': donor.active,
            'company_type': donor.company_type,
            'email': donor.email,
            'phone': donor.phone,
            'is_donor': donor.is_donor if hasattr(donor, 'is_donor') else False,
            'relation_began': donor.relation_began if hasattr(donor, 'relation_began') else False,
            'focal_person': donor.focal_person if hasattr(donor, 'focal_person') else False,
            'focal_person_email': donor.focal_person_email if hasattr(donor, 'focal_person_email') else False,
            'remarks': donor.remarks if hasattr(donor, 'remarks') else False,
            'company_id': donor.company_id.id if donor.company_id else False,
            'country_id': donor.country_id.id if donor.country_id else False,
            'parent_id': donor.parent_id.id if donor.parent_id else False,
            'currency_id': donor.currency_id.id if donor.currency_id else False
        }

    @api.model
    def update_donor(self, donor_id, vals):
        """
        Function to update a donor
        Args:
            donor_id: ID of the donor to update
            vals: Dictionary of values to update
        Returns: The json object of the updated donor
        """
        donor = self.env['res.partner'].sudo().browse(donor_id)
        if not donor.exists():
            return False
        donor.write(vals)
        return {
            'name': donor.name,
            'active': donor.active,
            'company_type': donor.company_type,
            'email': donor.email,
            'phone': donor.phone,
            'is_donor': donor.is_donor if hasattr(donor, 'is_donor') else False,
            'relation_began': donor.relation_began if hasattr(donor, 'relation_began') else False,
            'focal_person': donor.focal_person if hasattr(donor, 'focal_person') else False,
            'focal_person_email': donor.focal_person_email if hasattr(donor, 'focal_person_email') else False,
            'remarks': donor.remarks if hasattr(donor, 'remarks') else False,
            'company_id': donor.company_id.id if donor.company_id else False,
            'country_id': donor.country_id.id if donor.country_id else False,
            'parent_id': donor.parent_id.id if donor.parent_id else False,
            'currency_id': donor.currency_id.id if donor.currency_id else False
        }

    @api.model
    def delete_donor(self, donor_id):
        """
        Function to delete a donor
        Args:
            donor_id: ID of the donor to delete
        Returns: Boolean indicating success or failure
        """
        donor = self.env['res.partner'].sudo().browse(donor_id)
        if not donor.exists():
            return False
        return donor.unlink()
