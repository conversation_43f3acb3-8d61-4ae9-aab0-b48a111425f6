from odoo import models, fields, api

class HrJob(models.Model):
    _inherit = 'hr.job'

    _sql_constraints = [('job_name_unique', 'UNIQUE(name)', 'Designation must be unique!')]

    @api.model
    def get_active_designations(self):
        """
        Function to get all active jobs
        Returns: List of dictionaries containing id and name of active jobs
        """
        jobs = self.env['hr.job'].sudo().search([('active', '=', True)])
        return [{'id': job.id, 'name': job.name} for job in jobs]

    @api.model
    def get_designation(self, job_id):
        """
        Function to get a single job by ID
        Args:
            job_id: ID of the job to retrieve
        Returns: Dictionary containing job details or False if not found
        """
        job = self.env['hr.job'].sudo().browse(job_id)
        if not job.exists():
            return False
        return {
            'id': job.id,
            'name': job.name,
            'active': job.active
        }

    @api.model
    def create_designation(self, vals):
        """
        Function to create a job
        Returns: The json object of the created job
        """
        job = super(Hr<PERSON>ob, self).create(vals)
        return {
            'id': job.id,
            'name': job.name,
            'active': job.active
        }

    @api.model
    def update_designation(self, job_id, vals):
        """
        Function to update a job
        Args:
            job_id: ID of the job to update
            vals: Dictionary of values to update
        Returns: The json object of the updated job
        """
        job = self.env['hr.job'].sudo().browse(job_id)
        if not job.exists():
            return False
        job.write(vals)
        return {
            'id': job.id,
            'name': job.name,
            'active': job.active
        }

    @api.model
    def delete_designation(self, job_id):
        """
        Function to delete a job
        Args:
            job_id: ID of the job to delete
        Returns: Boolean indicating success or failure
        """
        job = self.env['hr.job'].sudo().browse(job_id)
        if not job.exists():
            return False
        return job.unlink()
