from odoo import models, _, fields, api
from odoo.exceptions import ValidationError, UserError

import logging
_logger = logging.getLogger(__name__)



_STATES = [
    ("draft", "Draft"),
    ("to_approve", "Finance Clearance"),
    ("hod_approve", "HOD Approval"),
    ("coo_approve", "COO Approval"),
    ("cfo_approve", "CFO Approval"),
    ("approved", "Approved"),
    ("rejected", "Rejected"),
    ("done", "Done"),
]

class PurchaseRequest(models.Model):
    _inherit = 'purchase.request'

    @api.model
    def create(self, vals):
        pr = super(PurchaseRequest, self).create(vals)

        if pr.pr_type == 'head_office' and pr.rel_emp:
            dept = pr.rel_emp.department_id
            if dept.need_coo_aproval and pr.estimated_cost >= dept.estimated_amount:
                pr.need_coo_approval = True
            else:
                pr.need_coo_approval = False
        return pr

    # @api.model
    # def create(self, vals):
    #     # Check if need_coo_approval should be set to True or False
    #     pr_types = vals.get("pr_type")
    #     rel_emp_id = vals.get("rel_emp")
    #     _logger.info(f"Keys in vals: {vals.keys()}")
    #     _logger.info(f"Creating Purchase Request with pr_type: {pr_types}, rel_emp_id: {rel_emp_id}, estimated_cost: {vals.get('estimated_cost', 0)}")
    #     if pr_types == 'head_office':
    #         if rel_emp_id:
    #             rel_emp = self.env["hr.employee"].browse(rel_emp_id)
    #             if rel_emp.department_id.need_coo_aproval and vals.get("estimated_cost", 0) >= rel_emp.department_id.estimated_amount:
    #                 vals["need_coo_approval"] = True
    #             else:
    #                 vals["need_coo_approval"] = False
        
    #     return super(PurchaseRequest, self).create(vals)

    # @api.model
    # def write(self, vals):
    #     # if "rel_emp" in vals or "estimated_cost" in vals:
    #     #     for rec in self:
    #     #         rel_emp_id = vals.get("rel_emp", rec.rel_emp.id if rec.rel_emp else False)
    #     #         estimated_cost = vals.get("estimated_cost", rec.estimated_cost)
    #     #         if rec.pr_type == 'head_office' and rel_emp_id:
    #     #             rel_emp = self.env["hr.employee"].browse(rel_emp_id)
    #     #             if rel_emp.department_id.need_coo_aproval and estimated_cost >= rel_emp.department_id.estimated_amount:
    #     #                 vals["need_coo_approval"] = True
    #     #             else:
    #     #                 vals["need_coo_approval"] = False
    #     _logger.info(f"Updating Purchase Request with vals: {vals}")
    #     if vals.get('need_coo_approval', False):
    #         for rec in self:
    #             if rec.hod_approve_by != rec.env.user or not rec.env.user.has_group("purchase_request.purchase_request_hod_user"):
    #                 raise ValidationError(_("You are not allowed to modify this record as it requires only HOD Users."))
                        
    #     return super(PurchaseRequest, self).write(vals)

    # @api.model
    # def write(self, vals):
    #     if "rel_emp" in vals or "estimated_cost" in vals:
    #         for rec in self:
    #             if rec.hod_approve_by == rec.env.user or rec.env.user.has_group("purchase_request.purchase_request_hod_user"):
    #                 rel_emp_id = vals.get("rel_emp", rec.rel_emp.id if rec.rel_emp else False)
    #                 estimated_cost = vals.get("estimated_cost", rec.estimated_cost)
    #                 if rec.pr_type == 'head_office' and rel_emp_id:
    #                     rel_emp = self.env["hr.employee"].browse(rel_emp_id)
    #                     if rel_emp.department_id.need_coo_aproval and estimated_cost >= rel_emp.department_id.estimated_amount:
    #                         vals["need_coo_approval"] = True
    #                     else:
    #                         vals["need_coo_approval"] = False
    #     return super(PurchaseRequest, self).write(vals)

    need_coo_approval = fields.Boolean(
        string="Need COO Approval",
        default=False,
        help="Check this box if the purchase request needs COO approval.",
    )
    verified_expert_authorization = fields.Boolean(
        string="Verified from Expert Authority?",
        default=False,
        help="Check this box if the purchase request has been verified by an expert.",
    )
    
    state = fields.Selection(
        selection=_STATES,
        string="Status",
        index=True,
        tracking=True,
        required=True,
        copy=False,
        default="draft",
    )

    def finance_approve(self):
        for rec in self:
            rec.finance_approve_by = rec.env.user.id
            rec.write({"state": "hod_approve"})

    def coo_approve(self):
        for rec in self:
            rec.coo_approve_by = rec.env.user.id
            rec.write({"state": "cfo_approve"})

    def send_to_hod(self):
        for rec in self:
            if rec.hod_approve_by and rec.finance_approve_by:
                if not "PR-" in rec.name:
                    rec.name = rec._get_default_name()
                rec.write({"state": "to_approve"})
            else:
                raise ValidationError(f"Both HOD Approver and Finance Approver must be selected to proceed!")

    def hod_approve_button(self):
        for rec in self:
            if rec.hod_approve_by:
                # if rec.budget_line_id:
                rec.update_budget_pr_cost()
                if rec.hod_approve_by == rec.env.user or rec.env.user.has_group("purchase_request.purchase_request_hod_user"):
                    if rec.need_coo_approval:
                        if rec.hod_approve_by != rec.env.user:
                            rec.hod_approve_by = rec.env.user.id 
                        rec.write({"state": "coo_approve"})
                    else:
                        if rec.hod_approve_by != rec.env.user:
                            rec.hod_approve_by = rec.env.user.id 
                        rec.write({"state": "cfo_approve"})
                else:
                    raise ValidationError(_("You are not allowed to approve this"))
            else:
                raise ValidationError(
                    _(
                        "Requester not select the HOD approver, please reject and reset then inform the requester to select HOD approver."
                    )
                )

    local_state = fields.Selection([
        ("draft", "Draft"),
        ("finance_approve", "Finance Approval"),
        ("manager_approve", "Manager Approval"),
        ('rc_approve', 'RC Approval'),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("done", "Done"),
    ], string='Local PR Status', default='draft')

    def send_to_manager(self):
        for rec in self:
            if not "PR-" in rec.name:
                rec.name = rec._get_default_name()
            rec.write({"local_state": "finance_approve"})

    def manager_approve(self):
        current_user = self.env.user
        for rec in self:
            if rec.hod_approve_by and rec.hod_approve_by.id == current_user.id:
                rec.write({"local_state": "rc_approve"})
            else:
                raise UserError(_("Only the assigned HOD manager can approve this request."))

    def finance_approve_button(self):
        current_user = self.env.user
        for rec in self:
            if rec.finance_user_id and rec.finance_user_id.id == current_user.id:
                rec.write({"local_state": "manager_approve"})
            else:
                raise UserError(_("Only the assigned Finance user can approve this request."))

    def rc_approve(self):
        current_user = self.env.user
        for rec in self:
            if rec.rc_user_id and rec.rc_user_id.id == current_user.id:
                rec.write({"local_state": "approved"})
            else:
                raise UserError(_("Only the assigned RC user can approve this request."))
