# -*- coding: utf-8 -*-
# from odoo import http


# class MetaPurchaseRequestApproval(http.Controller):
#     @http.route('/meta_purchase_request_approval/meta_purchase_request_approval', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/meta_purchase_request_approval/meta_purchase_request_approval/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('meta_purchase_request_approval.listing', {
#             'root': '/meta_purchase_request_approval/meta_purchase_request_approval',
#             'objects': http.request.env['meta_purchase_request_approval.meta_purchase_request_approval'].search([]),
#         })

#     @http.route('/meta_purchase_request_approval/meta_purchase_request_approval/objects/<model("meta_purchase_request_approval.meta_purchase_request_approval"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('meta_purchase_request_approval.object', {
#             'object': obj
#         })

